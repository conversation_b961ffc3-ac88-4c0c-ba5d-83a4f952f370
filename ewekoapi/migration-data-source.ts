import { DataSource } from 'typeorm';
import { 
  <PERSON><PERSON>, 
  <PERSON>, 
  Buyer, 
  Ad<PERSON>, 
  PhoneNumber, 
  Address, 
  Otp, 
  Cart, 
  CartItem, 
  Produce, 
  Order, 
  Wallet, 
  Preferences, 
  Notification 
} from './src/users/entities/user.entity';

export const AppDataSource = new DataSource({
  type: 'postgres',
  host: process.env.DB_HOST || 'localhost',
  port: Number(process.env.DB_PORT) || 5432,
  username: process.env.DB_USERNAME || 'postgres',
  password: process.env.DB_PASSWORD || 'password',
  database: process.env.DB_NAME || 'eweko_db',
  entities: [
    User,
    Farmer,
    Buyer,
    Admin,
    PhoneNumber,
    Address,
    Otp,
    Cart,
    CartItem,
    Produce,
    Order,
    Wallet,
    Preferences,
    Notification,
  ],
  migrations: ['src/migration/*{.ts,.js}'],
  synchronize: false,
  logging: true,
});
