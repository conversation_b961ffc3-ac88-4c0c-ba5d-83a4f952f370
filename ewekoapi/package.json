{"name": "eweko-api", "version": "2.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"clean": "npx rimraf dist", "build": "npm run clean && nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "sentry:sourcemaps": "sentry-cli sourcemaps inject --org eweko-integrated-service --project eweko-api ./dist && sentry-cli sourcemaps upload --org eweko-integrated-service --project eweko-api ./dist", "typeorm": "typeorm-ts-node-commonjs", "migration:generate": "npm run typeorm -- migration:generate -d data-source.ts src/migration/AutoMigration", "migration:run": "npm run typeorm -- migration:run -d data-source.ts", "migration:revert": "npm run typeorm -- migration:revert -d data-source.ts"}, "dependencies": {"@nestjs/common": "^11.1.3", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.3", "@nestjs/event-emitter": "^3.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/mapped-types": "^2.1.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.3", "@nestjs/platform-socket.io": "^11.1.3", "@nestjs/schedule": "^6.0.0", "@nestjs/serve-static": "^5.0.3", "@nestjs/swagger": "^11.2.0", "@nestjs/throttler": "^6.4.0", "@nestjs/typeorm": "^11.0.0", "@nestjs/websockets": "^11.1.3", "@sendgrid/mail": "^8.1.5", "@sentry/cli": "^2.46.0", "@sentry/nestjs": "^9.35.0", "@sentry/node": "^9.35.0", "@sentry/profiling-node": "^9.35.0", "@types/bcrypt": "^5.0.2", "@types/compression": "^1.8.1", "@types/cookie-parser": "^1.4.9", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/passport-jwt": "^4.0.1", "axios": "^1.10.0", "axios-retry": "^4.5.0", "bcryptjs": "^3.0.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "compression": "^1.8.0", "cookie-parser": "^1.4.7", "date-fns": "^4.1.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "libphonenumber-js": "^1.12.9", "multer": "^2.0.1", "nestjs": "^0.0.1", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pg": "^8.16.3", "reflect-metadata": "^0.2.2", "rimraf": "^6.0.1", "rxjs": "^7.8.2", "twilio": "^5.7.2", "typeorm": "^0.3.25"}, "devDependencies": {"@nestjs/cli": "^11.0.7", "@nestjs/schematics": "^11.0.5", "@nestjs/testing": "^11.1.3", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/multer": "^2.0.0", "@types/node": "^24.0.10", "@types/pg": "^8.15.4", "@types/supertest": "^6.0.3", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "eslint": "^9.30.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "jest": "^30.0.4", "prettier": "^3.6.2", "source-map-support": "^0.5.21", "supertest": "^7.1.1", "ts-jest": "^29.4.0", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}