import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';

export type CategoryDocument = HydratedDocument<Category>;

@Schema({ timestamps: true })
export class Category {
  @Prop({ required: true })
  name: string;

  @Prop({ required: true, unique: true })
  slug: string;

  @Prop({ type: [Types.ObjectId], ref: 'Produce', default: [] })
  produces: Types.ObjectId[];
}

export const CategorySchema = SchemaFactory.createForClass(Category);
 