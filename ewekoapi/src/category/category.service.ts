import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Category } from '../users/entities/user.entity';
import { slugify } from 'src/shared/utils';

@Injectable()
export class CategoryService {
  constructor(
    @InjectRepository(Category)
    private readonly categoryRepository: Repository<Category>,
  ) {}

  // Create a new category
  async create(createCategoryDto: CreateCategoryDto): Promise<Category> {
    // Check if a category with the same name already exists
    const existingCategory = await this.categoryRepository.findOne({
      where: { name: createCategoryDto.name },
    });

    if (existingCategory) {
      throw new BadRequestException('Category name already exists');
    }

    // Create new category
    const category = this.categoryRepository.create({
      ...createCategoryDto,
      slug: slugify(createCategoryDto.name),
    });

    return await this.categoryRepository.save(category);
  }

  // Find all categories
  async findAll(): Promise<Category[]> {
    return await this.categoryRepository.find({
      relations: ['produces']
    });
  }

  // Find a single category by ID
  async findOne(id: string): Promise<Category> {
    const category = await this.categoryRepository.findOne({
      where: { id },
      relations: ['produces']
    });
    if (!category) {
      throw new NotFoundException(`Category with id ${id} not found`);
    }
    return category;
  }

  // Update a category by ID
  async update(
    id: string,
    updateCategoryDto: UpdateCategoryDto,
  ): Promise<Category> {
    const updateData = { ...updateCategoryDto };
    if (updateCategoryDto.name) {
      updateData.slug = slugify(updateCategoryDto.name);
    }

    const result = await this.categoryRepository.update(id, updateData);
    if (result.affected === 0) {
      throw new NotFoundException(`Category with id ${id} not found`);
    }

    return this.findOne(id);
  }

  // Remove a category by ID
  async remove(id: string): Promise<void> {
    const result = await this.categoryRepository.delete(id);
    if (result.affected === 0) {
      throw new NotFoundException(`Category with id ${id} not found`);
    }
  }
}
