import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Category } from '../users/entities/user.entity';
import { slugify } from 'src/shared/utils';

@Injectable()
export class CategoryService {
  constructor(
    @InjectRepository(Category)
    private readonly categoryRepository: Repository<Category>,
  ) {}

  // Create a new category
  async create(createCategoryDto: CreateCategoryDto): Promise<Category> {
    // Check if a category with the same name already exists
    const existingCategory = await this.categoryModel.findOne({
      name: createCategoryDto.name,
    });

    if (existingCategory) {
      throw new BadRequestException('Category name already exists');
    }

    // Create new category
    const category = new this.categoryModel(createCategoryDto);
    category.slug = slugify(category.name);

    return await category.save();
  }

  // Find all categories
  async findAll(): Promise<Category[]> {
    return await this.categoryModel.find().populate('produces').exec();
  }

  // Find a single category by ID
  async findOne(id: string): Promise<Category> {
    const category = await this.categoryModel
      .findById(new Types.ObjectId(id))
      .exec();
    if (!category) {
      throw new NotFoundException(`Category with id ${id} not found`);
    }
    return category;
  }

  // Update a category by ID
  async update(
    id: string,
    updateCategoryDto: UpdateCategoryDto,
  ): Promise<Category> {
    const category = await this.categoryModel
      .findByIdAndUpdate(new Types.ObjectId(id), updateCategoryDto, {
        new: true,
      })
      .exec();
    if (!category) {
      throw new NotFoundException(`Category with id ${id} not found`);
    }
    return category;
  }

  // Remove a category by ID
  async remove(id: string): Promise<void> {
    const category = await this.categoryModel
      .findByIdAndDelete(new Types.ObjectId(id))
      .exec();
    if (!category) {
      throw new NotFoundException(`Category with id ${id} not found`);
    }
  }
}
