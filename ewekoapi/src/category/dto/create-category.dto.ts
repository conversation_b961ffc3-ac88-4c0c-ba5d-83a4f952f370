import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsOptional, IsString, IsMongoId } from 'class-validator';
import { Types } from 'mongoose';

export class CreateCategoryDto {
  @ApiProperty({
    description: 'The name of the category',
    example: 'Fruits',
    required: true,
  })
  @IsString()
  name: string;
}

export class CategoryResponseDto {
  @ApiProperty({
    description: 'The unique identifier of the category',
    example: '60a1e26f9c927b8b887a26a1',
  })
  _id: Types.ObjectId;

  @ApiProperty({
    description: 'The name of the category',
    example: 'Electronics',
  })
  name: string;

  @ApiProperty({
    description: 'The slug of the category',
    example: 'electronics',
  })
  slug: string;

  @ApiProperty({
    description: 'A brief description of the category',
    example: 'All kinds of electronic devices',
    required: false,
  })
  description?: string;

  @ApiProperty({
    description: 'List of children categories',
    example: ['60a1e26f9c927b8b887a26a2', '60a1e26f9c927b8b887a26a3'],
    required: false,
  })
  children: Types.ObjectId[];

  @ApiProperty({
    description: 'The parent category if any',
    example: '60a1e26f9c927b8b887a26a1',
    required: false,
  })
  parent: Types.ObjectId;

  @ApiProperty({
    description: 'List of products associated with this category',
    example: ['60a1e26f9c927b8b887a26a4', '60a1e26f9c927b8b887a26a5'],
    required: false,
  })
  products: Types.ObjectId[];
}
