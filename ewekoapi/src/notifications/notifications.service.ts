import {
  BadRequestException,
  HttpException,
  HttpStatus,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { OnEvent } from '@nestjs/event-emitter';
import { InjectRepository } from '@nestjs/typeorm';
import { SchedulerRegistry } from '@nestjs/schedule';
import { Repository } from 'typeorm';
import { Twilio } from 'twilio';
import * as sendGrid from '@sendgrid/mail';
import { NotificationDestination, NotificationTrigger } from 'src/shared/enums';
import { PaginationQueryDto } from 'src/shared/pagination/pagination-query.dto';
import { PaginationService } from 'src/shared/pagination/pagination.service';
import { UsersService } from 'src/users/users.service';
import { User, Notification, PhoneNumber } from 'src/users/entities/user.entity';
import { validate as isUUID } from 'uuid';

@Injectable()
export class NotificationsService {
  private readonly logger = new Logger(NotificationsService.name);
  private lastNotification: Notification | null = null;
  private notificationCount = 0;
  private client: Twilio;

  constructor(
    private configService: ConfigService,
    private schedulerRegistry: SchedulerRegistry,
    @InjectRepository(Notification)
    private readonly notificationRepository: Repository<Notification>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(PhoneNumber)
    private readonly phoneRepository: Repository<PhoneNumber>,
    private readonly paginationService: PaginationService,
    private readonly userService: UsersService,
  ) {}

  @OnEvent('user.created', { async: true })
  async welcomeNewUser(data: any) {
    try {
      const ewekoMail = this.configService.get<string>('EWEKO_MAIL');
      const notificationData = {
        user_id: data.id,
        title: 'Welcome to Eweko Aggregate',
        message: `Hello ${data.firstName}, thank you for joining Eweko Aggregate! We are excited to have you on board. Start exploring our services and products right away. If you have any questions or need assistance, contact our support team. Login and verify your account to get started today.`,
        type: 'info',
        read: false,
      };
      const savedNotification = await this.notificationRepository.save(notificationData);

      const user = await this.userService.findById(data.id);

      if (!user) {
        throw new NotFoundException(`User not found`);
      }
      user.notifications = [
        ...(user.notifications || []),
        savedNotification._id,
      ];
      user.save();

      if (savedNotification._id) {
        this.lastNotification = savedNotification;
        const contact = await this.contactModel.findById(data._doc.contact);

        if (otpDestination === NotificationDestination.EMAIL) {
          const to = contact.email;
          const name = data.firstName;

          const msg = {
            to,
            from: ewekoMail,
            subject: savedNotification.subject,
            templateId: 'd-545070718331464d9c3aa85e9f96b37a',
            dynamicTemplateData: {
              name,
            },
          };

          const email_sent = await this.sendMail(msg);

          if (email_sent.statusCode === 202) {
            this.logger.log(`Welcome email sent to user: ${to}`);
          } else {
            this.logger.error(`Failed to send email to user: ${to}`);
          }
        }

        if (otpDestination === NotificationDestination.SMS) {
          console.log('============================');
          console.log(`Sending SMS notification to: ${contact.primaryPhone}`);
          console.log('============================');

          const message = `Hello ${data.firstName}, thank you for joining Eweko Aggregate! We are excited to have you on board.`;

          // Add your SMS sending logic here
          const sms_sent: any = await this.sendSMS(
            contact.primaryPhone,
            message,
          );

          if (sms_sent.success) {
            this.logger.log(`SMS sent to user: ${contact.primaryPhone}`);
          } else {
            this.logger.error(
              `Failed to send SMS to user: ${contact.primaryPhone}`,
            );
          }
        }

        // After processing all destinations, return the response
        return {
          success: true,
          statusCode: HttpStatus.OK,
          message: `Welcome notifications sent to user`,
          data: null,
        };
      }
    } catch (error) {
      this.logger.error(error.message);
      return {
        success: false,
        statusCode: HttpStatus.BAD_REQUEST,
        message: error.message,
        data: null,
      };
    }
  }

  @OnEvent('verify.user', { async: true })
  async verifyNewUser(data: any) {
    try {
      console.log(data);

      // { code: '020163', useCase: 'VRFY_PHONE', destination: 'SMS' }

      const ewekoMail = this.configService.get<string>('EWEKO_MAIL');
      const message =
        data.otpDestination === NotificationDestination.EMAIL
          ? `Hello ${data.firstName}, kindly verify your account using this OTP: ${data.code} to start exploring our services and products right away. OTP expires in 5 minutes, so hurry.`
          : `Hello ${data.firstName}, kindly verify your account using this OTP: ${data.code}. Expires in 5mins`;

      const notificationData = {
        userType: data.userType,
        userId: data.id,
        subject: 'Verify Your Account',
        message: message,
        trigger: NotificationTrigger.USER_VERIFICATION,
        destinations: data.destination,
      };

      console.log(notificationData);

      const saveNotification = new this.notificationModel(notificationData);
      const savedNotification = await saveNotification.save();

      const { user } = await this.userService.findById(data.id);

      if (!user) {
        throw new NotFoundException(`User not found`);
      }
      user.notifications = [
        ...(user.notifications || []),
        savedNotification._id,
      ];
      user.save();

      if (savedNotification._id) {
        this.lastNotification = savedNotification;
        if (data.destination === NotificationDestination.EMAIL) {
          const to = data.email;
          const name = data.firstName;

          const msg = {
            to,
            from: ewekoMail,
            subject: savedNotification.subject,
            templateId: 'd-d7bf647cbc1b4bf1985f71e97b651877',
            dynamicTemplateData: {
              name,
              otp: data.code,
            },
          };
          const email_sent = await this.sendMail(msg);

          if (email_sent.statusCode === 202) {
            this.logger.log(`Welcome email sent to user: ${to}`);
          } else {
            this.logger.error(`Failed to send email to user: ${to}`);
          }
        }

        if (data.destination === NotificationDestination.SMS) {
          console.log('============================');
          console.log(`Sending SMS notification to: ${data.phoneNumber}`);
          console.log('============================');

          // Add your SMS sending logic here
          const sms_sent: any = await this.sendSMS(
            data.phoneNumber,
            savedNotification.message,
          );

          if (sms_sent.success) {
            this.logger.log(`SMS sent to user: ${data.phoneNumber}`);
          } else {
            this.logger.error(
              `Failed to send SMS to user: ${data.phoneNumber}`,
            );
          }
        }

        // After processing all destinations, return the response
        return {
          success: true,
          statusCode: HttpStatus.OK,
          message: `Welcome notifications sent to user`,
          data: null,
        };
      }
    } catch (error) {
      this.logger.error(error.message);
      return {
        success: false,
        statusCode: HttpStatus.BAD_REQUEST,
        message: error.message,
        data: null,
      };
    }
  }

  @OnEvent('verify.2fa', { async: true })
  async verify2fa(data: any) {
    try {
      console.log(data);

      // { code: '020163', useCase: 'VRFY_PHONE', destination: 'SMS' }

      const ewekoMail = this.configService.get<string>('EWEKO_MAIL');
      const message =
        data.otpDestination === NotificationDestination.EMAIL
          ? `Hello ${data.firstName}, kindly verify your login using this OTP: ${data.code} to access your dashboard. OTP expires in 5 minutes, so hurry.`
          : `Hello ${data.firstName}, kindly verify your login using this OTP: ${data.code}. Expires in 5mins`;

      const notificationData = {
        userType: data.userType,
        userId: data.id,
        subject: 'Verify Your Login',
        message: message,
        trigger: NotificationTrigger.USER_VERIFICATION,
        destinations: data.destination,
      };

      console.log(notificationData);

      const saveNotification = new this.notificationModel(notificationData);
      const savedNotification = await saveNotification.save();

      console.log('Saved notification:', savedNotification);

      // const { user } = await this.userService.findById(data.id);

      // if (!user) {
      //   throw new NotFoundException(`User not found`);
      // }
      // user.notifications = [
      //   ...(user.notifications || []),
      //   savedNotification._id,
      // ];
      // user.save();

      // if (savedNotification._id) {
      //   this.lastNotification = savedNotification;

      //   // for (let destination of savedNotification.destinations) {
      //   //   console.log(destination);

      //   //   if (otpDestination === NotificationDestination.EMAIL) {
      //   //     const to = data.email;
      //   //     const name = data.firstName;

      //   //     const msg = {
      //   //       to,
      //   //       from: ewekoMail,
      //   //       subject: savedNotification.subject,
      //   //       templateId: 'd-d7bf647cbc1b4bf1985f71e97b651877',
      //   //       dynamicTemplateData: {
      //   //         name,
      //   //         otp: data.code,
      //   //       },
      //   //     };
      //   //     const email_sent = await this.sendMail(msg);

      //   //     if (email_sent.statusCode === 202) {
      //   //       this.logger.log(`Welcome email sent to user: ${to}`);
      //   //     } else {
      //   //       this.logger.error(`Failed to send email to user: ${to}`);
      //   //     }
      //   //   }

      //   //   if (otpDestination === NotificationDestination.SMS) {
      //   //     console.log('============================');
      //   //     console.log(`Sending SMS notification to: ${data.phoneNumber}`);
      //   //     console.log('============================');

      //   //     // Add your SMS sending logic here
      //   //     const sms_sent: any = await this.sendSMS(
      //   //       data.phoneNumber,
      //   //       savedNotification.message,
      //   //     );

      //   //     if (sms_sent.success) {
      //   //       this.logger.log(`SMS sent to user: ${data.phoneNumber}`);
      //   //     } else {
      //   //       this.logger.error(
      //   //         `Failed to send SMS to user: ${data.phoneNumber}`,
      //   //       );
      //   //     }
      //   //   }
      //   // }

      //   // After processing all destinations, return the response
      //   return {
      //     success: true,
      //     statusCode: HttpStatus.OK,
      //     message: `Welcome notifications sent to user`,
      //     data: null,
      //   };
      // }
    } catch (error) {
      this.logger.error(error.message);
      return {
        success: false,
        statusCode: HttpStatus.BAD_REQUEST,
        message: error.message,
        data: null,
      };
    }
  }

  @OnEvent('forgot-password.email', { async: true })
  async forgotPasswordEmail(data: any) {
    try {
      const ewekoMail = this.configService.get<string>('EWEKO_MAIL');
      const message =
        data.otpDestination ===
        `Hello ${data.firstName}, kindly use the following link to reset your EwekoAggregate account password: ${data.code}.`;

      const notificationData = {
        userType: data.userType,
        userId: data.id,
        subject: 'Reset Your Password',
        message: message,
        trigger: NotificationTrigger.PASSWORD_RESET,
        destinations: data.destination,
      };

      const saveNotification = new this.notificationModel(notificationData);
      const savedNotification = await saveNotification.save();

      const { user } = await this.userService.findById(data.id);

      if (!user) {
        throw new NotFoundException(`User not found`);
      }

      user.notifications = [
        ...(user.notifications || []),
        savedNotification._id,
      ];
      user.save();

      if (savedNotification._id) {
        this.lastNotification = savedNotification;

        const { otpDestination } = await this.preferenceModel.findOne({
          user: user._id,
        });

        const contact = await this.contactModel.findById(user.contact);

        if (otpDestination === NotificationDestination.EMAIL) {
          const to = contact.email;
          const name = data.firstName;
          const msg = {
            to,
            from: ewekoMail,
            subject: savedNotification.subject,
            templateId: 'd-15bf7b5cef6d4d5c9f0f6d7b8a00a28c',
            dynamicTemplateData: {
              name,
              link: data.resetLink,
            },
          };
          const email_sent = await this.sendMail(msg);

          if (email_sent.statusCode === 202) {
            this.logger.log(`Password reset email sent to user: ${to}`);
          } else {
            this.logger.error(`Failed to send email to user: ${to}`);
          }
        }

        if (otpDestination === NotificationDestination.SMS) {
          console.log('============================');
          console.log(`Sending SMS notification to: ${contact.primaryPhone}`);
          console.log('============================');

          // Add your SMS sending logic here
          const sms_sent: any = await this.sendSMS(
            contact.primaryPhone,
            savedNotification.message,
          );

          if (sms_sent.success) {
            this.logger.log(`SMS sent to user: ${data.phoneNumber}`);
          } else {
            this.logger.error(
              `Failed to send SMS to user: ${data.phoneNumber}`,
            );
          }
        }

        if (otpDestination === NotificationDestination.BOTH) {
          const to = contact.email;
          const name = data.firstName;
          const msg = {
            to,
            from: ewekoMail,
            subject: savedNotification.subject,
            templateId: 'd-15bf7b5cef6d4d5c9f0f6d7b8a00a28c',
            dynamicTemplateData: {
              name,
              link: data.resetLink,
            },
          };
          const email_sent = await this.sendMail(msg);

          if (email_sent.statusCode === 202) {
            this.logger.log(`Password reset email sent to user: ${to}`);
          } else {
            this.logger.error(`Failed to send email to user: ${to}`);
          } 

          console.log('============================');
          console.log(`Sending SMS notification to: ${contact.primaryPhone}`);
          console.log('============================');

          // Add your SMS sending logic here
          const sms_sent: any = await this.sendSMS(
            contact.primaryPhone,
            savedNotification.message,
          );

          if (sms_sent.success) {
            this.logger.log(`SMS sent to user: ${data.phoneNumber}`);
          } else {
            this.logger.error(
              `Failed to send SMS to user: ${data.phoneNumber}`,
            );
          }
        }

        // After processing all destinations, return the response

        return {
          success: true,
          statusCode: HttpStatus.OK,
          message: `Password reset email sent to user`,
          data: null,
        };
      }
    } catch (error) {
      this.logger.error(error.message);
      return {
        success: false,
        statusCode: HttpStatus.BAD_REQUEST,
        message: error.message,
        data: null,
      };
    }
  }

  @OnEvent('forgot-password.phone', { async: true })
  async forgotPasswordPhone(data: any) {
    try {
      console.log(data);

      // { code: '020163', useCase: 'VRFY_PHONE', destination: 'SMS' }

      const ewekoMail = this.configService.get<string>('EWEKO_MAIL');
      const message = `Hello ${data.firstName}, Kindly use this ${data.code} to reset your EwekoAggregate password. Expires in 5mins`;

      const notificationData = {
        userType: data.userType,
        userId: data.id,
        subject: 'Reset Your Password',
        message: message,
        trigger: NotificationTrigger.PASSWORD_RESET,
        destinations: data.destination,
      };

      console.log(notificationData);

      const saveNotification = new this.notificationModel(notificationData);
      const savedNotification = await saveNotification.save();

      const { user } = await this.userService.findById(data.id);

      if (!user) {
        throw new NotFoundException(`User not found`);
      }

      user.notifications = [
        ...(user.notifications || []),
        savedNotification._id,
      ];
      user.save();

      if (savedNotification._id) {
        this.lastNotification = savedNotification;

        // if (otpDestination === NotificationDestination.EMAIL) {
        //   const to = data.email;
        //   const name = data.firstName;

        //   const msg = {
        //     to,
        //     from: ewekoMail,
        //     subject: savedNotification.subject,
        //     templateId: 'd-d7bf647cbc1b4bf1985f71e97b651877',
        //     dynamicTemplateData: {
        //       name,
        //       otp: data.code,
        //     },
        //   };
        //   const email_sent = await this.sendMail(msg);

        //   if (email_sent.statusCode === 202) {
        //     this.logger.log(`Welcome email sent to user: ${to}`);
        //   } else {
        //     this.logger.error(`Failed to send email to user: ${to}`);
        //   }
        // }

        // if (otpDestination === NotificationDestination.SMS) {
        //   // Add your SMS sending logic here
        //   await this.sendSMS(data.phoneNumber, savedNotification.message);
        // }

        // After processing all destinations, return the response

        return {
          success: true,
          statusCode: HttpStatus.OK,
          message: `Password reset OTP sent to user`,
          data: null,
        };
      }
    } catch (error) {
      this.logger.error(error.message);
      return {
        success: false,
        statusCode: HttpStatus.BAD_REQUEST,
        message: error.message,
        data: null,
      };
    }
  }

  async sendMail(mail: sendGrid.MailDataRequired) {
    sendGrid.setApiKey(this.configService.get<string>('SENDGRID_API_KEY'));
    // console.log(mail)

    try {
      const responses = await sendGrid.send(mail);
      const res = responses[0];

      return res;
    } catch (error) {
      this.logger.error(error);

      throw new HttpException(
        { message: 'Failed to send email. Please try again.', error },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async sendSMS(
    phoneNumber: string,
    text: string,
    // statusCallback: any,
  ): Promise<void> {
    try {
      const accountSid = this.configService.get<string>('TWILIO_ACCOUNT_SID');
      const authToken = this.configService.get<string>('TWILIO_AUTH_TOKEN');
      const twilioPhoneNumber = this.configService.get<string>(
        'TWILIO_PHONE_NUMBER',
      );

      if (!accountSid || !authToken || !twilioPhoneNumber) {
        throw new Error('Twilio credentials are not configured properly.');
      }

      const client = new Twilio(accountSid, authToken);

      const smsResponse = await client.messages.create({
        from: twilioPhoneNumber,
        to: phoneNumber,
        body: text,
        messagingServiceSid: 'MGdbd70e8b8605b9b6c3bd0745f9d11aa8',
        // statusCallback,
      });

      if (smsResponse.status === 'accepted')
        console.log('SMS sent successfully!');
    } catch (error) {
      console.error('Failed to send SMS:', error.message);
      console.log(error);
      error.statusCode = 400;
      throw error;
    }
  }

  async sendOtp(phoneNumber: string): Promise<{ success: string }> {
    try {
      const verifySid = this.configService.get<string>('TWILIO_VERIFY_SID');
      const verification = await this.client.verify.v2
        .services(verifySid)
        .verifications.create({
          channel: 'sms',
          to: phoneNumber,
        });

      if (verification.sid) {
        return {
          success: 'Verification OTP sent to your registered phone number',
        };
      }
    } catch (error) {
      console.error('Failed to send SMS:', error.message);
      throw new InternalServerErrorException('Failed to send SMS');
    }
  }

  async verifyOtp(
    phoneNumber: string,
    otp: string,
  ): Promise<{ message: string }> {
    try {
      const verifySid = this.configService.get<string>('TWILIO_VERIFY_SID');
      const verificationCheck = await this.client.verify.v2
        .services(verifySid)
        .verificationChecks.create({
          code: otp,
          to: phoneNumber,
        });

      if (verificationCheck.status === 'approved') {
        return {
          message: 'OTP verified successfully',
        };
      } else {
        throw new BadRequestException(
          'OTP is either incorrect or it has expired',
        );
      }
    } catch (error) {
      console.error('Failed to send SMS:', error.message);
      if (
        error.message ===
          'The requested resource /Services/VAe77432efecafc4d1a48da589ff82c746/VerificationCheck was not found' ||
        error.message === 'OTP is either incorrect or it has expired'
      ) {
        throw new BadRequestException(
          'OTP is either incorrect or it has expired',
        );
      } else if (error.message === 'Max check attempts reached') {
        throw new BadRequestException('Max check attempts reached');
      } else {
        throw new InternalServerErrorException('Failed to send SMS');
      }
    }
  }

  async findAll(userId: string, paginationQuery: PaginationQueryDto) {
    if (!isUUID(userId)) {
      throw new BadRequestException('Invalid user ID format');
    }
    const { page = 1, limit = 10 } = paginationQuery;

    // Fetch all notifications for the user
    const notifications = await this.notificationModel
      .find({ userId })
      .sort({ created_at: -1 })
      .skip((page - 1) * limit)
      .limit(limit)
      .exec();

    // Return paginated response
    return this.paginationService.paginate(notifications, {
      page,
      limit,
    });
  }

  async findUnreadNotifications(
    userId: string,
    paginationQuery: PaginationQueryDto,
  ) {
    if (!isUUID(userId)) {
      throw new BadRequestException('Invalid user ID');
    }
    const { page = 1, limit = 10 } = paginationQuery;

    // Fetch paginated unread notifications only
    const notifications = await this.notificationModel
      .find({
        userId,
        isRead: false,
      })
      .sort({ created_at: -1 })
      .skip((page - 1) * limit)
      .limit(limit)
      .exec();

    // Return paginated response
    return this.paginationService.paginate(notifications, {
      page,
      limit,
    });
  }

  async findReadNotifications(
    userId: string,
    paginationQuery: PaginationQueryDto,
  ) {
    if (!isUUID(userId)) {
      throw new BadRequestException('Invalid user ID');
    }
    const { page = 1, limit = 10 } = paginationQuery;

    // Fetch paginated read notifications only
    const notifications = await this.notificationModel
      .find({
        userId,
        isRead: true,
      })
      .sort({ created_at: -1 })
      .skip((page - 1) * limit)
      .limit(limit)
      .exec();

    // Return paginated response
    return this.paginationService.paginate(notifications, {
      page,
      limit,
    });
  }

  async findOne(userId: string, id: string) {
    if (!isUUID(userId) || !isUUID(id)) {
      throw new BadRequestException('Invalid ID format');
    }

    const notification = await this.notificationModel
      .findOne({ _id: id, userId })
      .exec();
    if (!notification) {
      throw new NotFoundException('Notification not found');
    }

    return notification;
  }

  async updateReadStatus(id: string) {
    if (!isUUID(id)) {
      throw new NotFoundException(`Invalid notification ID`);
    }

    const updatedNotification = await this.notificationModel.findByIdAndUpdate(
      id,
      { isRead: true },
      { new: true },
    );

    if (!updatedNotification) {
      throw new NotFoundException(`Notification not found`);
    }

    return updatedNotification;
  }

  async markAllAsRead(userId: string) {
    if (!isUUID(userId)) {
      throw new NotFoundException(`Invalid user ID format: ${userId}`);
    }
    const result = await this.notificationModel.updateMany(
      {
        userId,
        isRead: false,
      },
      { isRead: true },
    );

    return { updatedCount: result.modifiedCount };
  }

  async clearAll(userId: string): Promise<{ message: string }> {
    if (!isUUID(userId)) {
      throw new BadRequestException('Invalid user ID format');
    }

    try {
      await this.notificationModel.deleteMany({ userId }).exec();
      return { message: 'All notifications cleared successfully' };
    } catch (error) {
      this.logger.error(
        `Error clearing notifications for user ${userId}`,
        error,
      );
      throw new InternalServerErrorException('Could not clear notifications');
    }
  }
}
