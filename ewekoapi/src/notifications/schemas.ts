import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';
import {
  NotificationDestination,
  NotificationTrigger,
  UserType,
} from 'src/shared/enums';

@Schema({ timestamps: true })
export class Notification {
  @Prop({
    type: String,
    required: true,
    enum: Object.values(UserType),
    description: 'Type of user receiving the notification',
    example: UserType.BUYER,
  })
  userType: UserType;

  @Prop({ required: true })
  userId: string;

  @Prop({ required: true })
  subject: string;

  @Prop({ required: true })
  message: string;

  @Prop({
    type: String,
    enum: Object.values(NotificationTrigger),
    default: NotificationTrigger.USER_REGISTRATION,
    description: 'Trigger event for the notification',
  })
  trigger: NotificationTrigger;

  @Prop({
    default: false,
    description: 'Indicates if the notification has been read',
  })
  isRead: boolean;
}

export type NotificationDocument = HydratedDocument<Notification>;
export const NotificationSchema = SchemaFactory.createForClass(Notification);
