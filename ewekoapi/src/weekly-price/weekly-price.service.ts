import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Category } from '../category/schema';
import { 
  WeeklyCategoryPrice, 
  WeeklyCategoryPriceDocument,
} from './schema';
import { 
  CategoryWeeklyPriceDto,
  WeeklyPricesResponse
} from './dto/weekly-price.dto';

@Injectable()
export class WeeklyPriceService {
  private readonly logger = new Logger(WeeklyPriceService.name);

  constructor(
    @InjectModel(WeeklyCategoryPrice.name)
    private readonly weeklyPriceModel: Model<WeeklyCategoryPriceDocument>,
    @InjectModel(Category.name)
    private readonly categoryModel: Model<Category>,
  ) {}

  /**
   * Get the start of the week (Monday) for a given date
   * This ensures consistent weekly buckets for price aggregation
   */
  private getStartOfWeek(date: Date = new Date()): Date {
    const d = new Date(date);
    const day = d.getUTCDay();
    const diff = d.getUTCDate() - day + (day === 0 ? -6 : 1); // Adjust when day is Sunday
    d.setUTCDate(diff);
    d.setUTCHours(0, 0, 0, 0);
    return d;
  }

  /**
   * Calculate and store weekly price averages for all categories
   * This is called by the CronsService on a weekly schedule
   */
  async calculateWeeklyAverages() {
    const weekStart = this.getStartOfWeek();
    this.logger.log(`Starting weekly price calculation for week starting ${weekStart.toISOString()}`);

    try {
      // Get all categories with debug info
      const categories = await this.categoryModel.find({}).lean();
      this.logger.debug(`Found ${categories.length} categories to process`);
      
      // Calculate average price for each category
      for (const category of categories) {
        this.logger.debug(`Processing category: ${category.name} (${category._id})`);
        
        try {
          // First, check if there are any active produces for this category
          const activeProduces = await this.categoryModel.aggregate([
            { $match: { _id: category._id } },
            {
              $lookup: {
                from: 'produces',
                localField: '_id',
                foreignField: 'category',
                as: 'produces',
                pipeline: [
                  { $match: { stock: { $gt: 0 } } },
                  { $project: { _id: 1, name: 1, price: 1 } }
                ]
              }
            },
            { $unwind: '$produces' },
            { $replaceRoot: { newRoot: '$produces' } }
          ]).exec();

          this.logger.debug(`Found ${activeProduces.length} active produces for category ${category.name}`);
          
          if (activeProduces.length > 0) {
            // Log some sample produces for debugging
            activeProduces.slice(0, 3).forEach((p, i) => {
              this.logger.debug(`Produce ${i + 1}: ${p.name} - ${p.price}`);
            });
            if (activeProduces.length > 3) {
              this.logger.debug(`... and ${activeProduces.length - 3} more produces`);
            }

            // Calculate average price
            const totalPrice = activeProduces.reduce((sum, p) => sum + p.price, 0);
            const averagePrice = totalPrice / activeProduces.length;
            const roundedAvg = Math.round(averagePrice * 100) / 100;

            // Update or create the weekly price record
            await this.weeklyPriceModel.findOneAndUpdate(
              {
                category: category._id,
                weekStartDate: weekStart
              },
              {
                $set: {
                  averagePrice: roundedAvg,
                  totalListings: activeProduces.length
                }
              },
              { upsert: true, new: true }
            );

            this.logger.log(`✅ Updated weekly price for category "${category.name}": ${roundedAvg} (${activeProduces.length} listings)`);
          } else {
            this.logger.warn(`⚠️ No active produces found for category "${category.name}" (${category._id})`);
            
            // Debug: Check if there are any produces (active or inactive) for this category
            const anyProduces = await this.categoryModel.aggregate([
              { $match: { _id: category._id } },
              {
                $lookup: {
                  from: 'produces',
                  localField: '_id',
                  foreignField: 'category',
                  as: 'produces',
                  pipeline: [
                    { $project: { _id: 1, name: 1, status: 1 } }
                  ]
                }
              },
              { $unwind: '$produces' },
              { $replaceRoot: { newRoot: '$produces' } }
            ]).exec();
            
            if (anyProduces.length > 0) {
              this.logger.debug(`Found ${anyProduces.length} total produces (including inactive) for category ${category.name}:`);
              anyProduces.slice(0, 5).forEach(p => {
                this.logger.debug(`- ${p.name} (${p.status})`);
              });
            } else {
              this.logger.debug(`No produces found at all for category ${category.name}`);
            }
          }
        } catch (error) {
          this.logger.error(
            `Error calculating weekly average for category ${category.name}: ${error.message}`,
            error.stack
          );
        }
      }
      
      this.logger.log('Weekly price calculation completed');
    } catch (error) {
      this.logger.error(`Error in weekly price calculation: ${error.message}`, error.stack);
    }
  }

  /**
   * Get the latest weekly prices for all categories
   * Returns current and previous week's prices
   */
  async getLatestWeeklyPrices(): Promise<WeeklyPricesResponse> {
    const currentWeekStart = this.getStartOfWeek();
    const previousWeekStart = new Date(currentWeekStart);
    previousWeekStart.setDate(previousWeekStart.getDate() - 7);

    // Get all categories with their current and previous week prices
    const categories = await this.categoryModel.aggregate([
      {
        $lookup: {
          from: 'weeklycategoryprices',
          let: { categoryId: '$_id' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ['$category', '$$categoryId'] },
                    { $in: ['$weekStartDate', [currentWeekStart, previousWeekStart]] }
                  ]
                }
              }
            },
            {
              $project: {
                weekStartDate: 1,
                averagePrice: 1,
                isCurrentWeek: { $eq: ['$weekStartDate', currentWeekStart] }
              }
            }
          ],
          as: 'prices'
        }
      },
      {
        $project: {
          name: 1,
          currentPrice: {
            $let: {
              vars: {
                current: {
                  $arrayElemAt: [
                    { $filter: { input: '$prices', as: 'p', cond: { $eq: ['$$p.isCurrentWeek', true] } } },
                    0
                  ]
                }
              },
              in: '$$current.averagePrice'
            }
          },
          previousPrice: {
            $let: {
              vars: {
                previous: {
                  $arrayElemAt: [
                    { $filter: { input: '$prices', as: 'p', cond: { $eq: ['$$p.isCurrentWeek', false] } } },
                    0
                  ]
                }
              },
              in: '$$previous.averagePrice'
            }
          }
        }
      },
      { $match: { currentPrice: { $ne: null } } } // Only include categories with current price data
    ]);

    // Format the response
    const data: CategoryWeeklyPriceDto[] = categories.map(category => ({
      categoryName: category.name,
      currentPrice: category.currentPrice,
      previousPrice: category.previousPrice || 0 // Default to 0 if no previous price
    }));

    return { data };
  }
}
