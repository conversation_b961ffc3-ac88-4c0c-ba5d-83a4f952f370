import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type WeeklyCategoryPriceDocument = WeeklyCategoryPrice & Document;

@Schema({
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
})
export class WeeklyCategoryPrice {
  @Prop({ type: Types.ObjectId, ref: 'Category', required: true })
  category: Types.ObjectId;

  @Prop({ type: Date, required: true, index: true })
  weekStartDate: Date;

  @Prop({ type: Number, required: true, min: 0 })
  averagePrice: number;

  @Prop({ type: Number, required: true, min: 0 })
  totalListings: number;

  // Virtual for category name (will be populated in the service)
  categoryName?: string;
  categorySlug?: string;
}

// Index to ensure we only have one entry per category per week
const WeeklyCategoryPriceSchema = SchemaFactory.createForClass(WeeklyCategoryPrice);
WeeklyCategoryPriceSchema.index(
  { category: 1, weekStartDate: 1 },
  { unique: true },
);

export { WeeklyCategoryPriceSchema };
