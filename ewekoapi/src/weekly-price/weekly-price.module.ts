import { Module } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { MongooseModule } from '@nestjs/mongoose';
import { WeeklyPriceService } from './weekly-price.service';
import { WeeklyPriceController } from './weekly-price.controller';
import { WeeklyCategoryPrice, WeeklyCategoryPriceSchema } from './schema';
import { CategoryModule } from '../category/category.module';

@Module({
  imports: [
    ScheduleModule.forRoot(),
    MongooseModule.forFeature([
      { name: WeeklyCategoryPrice.name, schema: WeeklyCategoryPriceSchema },
    ]),
    CategoryModule,
  ],
  controllers: [WeeklyPriceController],
  providers: [WeeklyPriceService],
  exports: [WeeklyPriceService],
})
export class WeeklyPriceModule {}
