import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
} from '@nestjs/common';
import { AddressesService } from './addresses.service';
import { CreateAddressDto } from './dto/create-address.dto';
import { UpdateAddressDto } from './dto/update-address.dto';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiQuery,
  ApiParam,
} from '@nestjs/swagger';
import { Address } from './schema';
import { EwekoAuthGuard } from 'src/auth/jwt.guard';
import { Roles } from 'src/shared/decorators/roles.decorator';
import { UserType } from 'src/shared/enums';

@Controller('addresses')
@ApiTags('addresses')
@UseGuards(EwekoAuthGuard)
@Roles(UserType.ADMIN, UserType.BUYER)
export class AddressesController {
  constructor(private readonly addressesService: AddressesService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new address' })
  @ApiResponse({
    status: 201,
    description: 'Address created successfully',
    type: Address,
  })
  @ApiResponse({ status: 404, description: 'User not found' })
  create(@Body() createAddressDto: CreateAddressDto) {
    return this.addressesService.create(createAddressDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all addresses or filter by user ID' })
  @ApiQuery({
    name: 'userId',
    required: false,
    description: 'Filter addresses by user ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Addresses retrieved successfully',
    type: [Address],
  })
  findAll(@Query('userId') userId?: string) {
    return this.addressesService.findAll(userId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get an address by ID' })
  @ApiParam({ name: 'id', description: 'Address ID' })
  @ApiResponse({
    status: 200,
    description: 'Address retrieved successfully',
    type: Address,
  })
  @ApiResponse({ status: 404, description: 'Address not found' })
  findOne(@Param('id') id: string) {
    return this.addressesService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update an address by ID' })
  @ApiParam({ name: 'id', description: 'Address ID' })
  @ApiResponse({
    status: 200,
    description: 'Address updated successfully',
    type: Address,
  })
  @ApiResponse({ status: 404, description: 'Address not found' })
  update(@Param('id') id: string, @Body() updateAddressDto: UpdateAddressDto) {
    return this.addressesService.update(id, updateAddressDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete an address by ID' })
  @ApiParam({ name: 'id', description: 'Address ID' })
  @ApiResponse({
    status: 200,
    description: 'Address deleted successfully',
    type: Address,
  })
  @ApiResponse({ status: 404, description: 'Address not found' })
  remove(@Param('id') id: string) {
    return this.addressesService.remove(id);
  }
}
