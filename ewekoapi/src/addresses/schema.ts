import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';

export type AddressDocument = HydratedDocument<Address>;

@Schema({ timestamps: true })
export class Address {
  @Prop({ required: true })
  userId: Types.ObjectId;

  @Prop({ type: String, required: true })
  houseNumber: string;

  @Prop({ type: String, required: true })
  streetName: string;

  @Prop({ type: String, required: true })
  community: string;

  @Prop({ type: String, required: true })
  lga: string;

  @Prop({ type: String, required: true })
  state: string;

  // @Prop({ type: String, required: true })
  // country: string;

  @Prop({ type: Boolean, default: false })
  isDefault: boolean;
}

export const AddressSchema = SchemaFactory.createForClass(Address);
