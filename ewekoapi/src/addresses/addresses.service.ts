import {
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { CreateAddressDto } from './dto/create-address.dto';
import { UpdateAddressDto } from './dto/update-address.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { User, Address } from '../users/entities/user.entity';

@Injectable()
export class AddressesService {
  constructor(
    @InjectRepository(Address)
    private readonly addressRepository: Repository<Address>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly dataSource: DataSource,
  ) {}

  async create(createAddressDto: CreateAddressDto): Promise<Address> {
    const { userId, ...addressData } = createAddressDto;

    // Check if user exists
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Map DTO fields to database fields
    const mappedAddressData = {
      user_id: userId,
      street: `${addressData.houseNumber} ${addressData.streetName}`,
      city: addressData.lga,
      state: addressData.state,
      country: 'Nigeria', // Default country
      postal_code: null,
      type: 'home',
      is_primary: false,
    };

    // Check for duplicate address
    const existingDuplicate = await this.addressRepository.findOne({
      where: {
        user_id: userId,
        street: mappedAddressData.street,
        city: mappedAddressData.city,
        state: mappedAddressData.state,
      },
    });

    if (existingDuplicate) {
      throw new ConflictException('This address already exists for the user.');
    }

    // Check if this is the user's first address
    const addressCount = await this.addressRepository.count({ where: { user_id: userId } });
    const isFirstAddress = addressCount === 0;

    // Create new address
    const newAddress = this.addressRepository.create({
      ...mappedAddressData,
      is_primary: isFirstAddress || createAddressDto.isDefault || false,
    });

    return await this.addressRepository.save(newAddress);
  }

  async findAll(userId?: string): Promise<Address[]> {
    if (userId) {
      return await this.addressRepository.find({
        where: { user_id: userId },
        order: { created_at: 'DESC' }
      });
    }
    return await this.addressRepository.find({
      order: { created_at: 'DESC' }
    });
  }

  async findOne(id: string): Promise<Address> {
    const address = await this.addressRepository.findOne({ where: { id } });
    if (!address) {
      throw new NotFoundException(`Address not found.`);
    }
    return address;
  }

  async update(
    id: string,
    updateAddressDto: UpdateAddressDto,
  ): Promise<Address> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const address = await queryRunner.manager.findOne(Address, { where: { id } });

      if (!address) {
        throw new NotFoundException('Address not found.');
      }

      // Map DTO fields to database fields if they exist
      const updateData: Partial<Address> = {};

      if (updateAddressDto.houseNumber || updateAddressDto.streetName) {
        const houseNumber = updateAddressDto.houseNumber || address.street.split(' ')[0];
        const streetName = updateAddressDto.streetName || address.street.substring(address.street.indexOf(' ') + 1);
        updateData.street = `${houseNumber} ${streetName}`;
      }

      if (updateAddressDto.lga) updateData.city = updateAddressDto.lga;
      if (updateAddressDto.state) updateData.state = updateAddressDto.state;
      if (updateAddressDto.community) updateData.postal_code = updateAddressDto.community;

      // If this address is being marked as primary, unset others
      if (updateAddressDto.isDefault) {
        updateData.is_primary = true;
        await queryRunner.manager
          .createQueryBuilder()
          .update(Address)
          .set({ is_primary: false })
          .where('user_id = :userId AND id != :id', { userId: address.user_id, id })
          .execute();
      }

      // Update the address
      await queryRunner.manager.update(Address, { id }, updateData);

      const updatedAddress = await queryRunner.manager.findOne(Address, { where: { id } });

      if (!updatedAddress) {
        throw new NotFoundException('Address not found after update.');
      }

      await queryRunner.commitTransaction();
      return updatedAddress;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async remove(id: string): Promise<Address> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const addressToDelete = await queryRunner.manager.findOne(Address, { where: { id } });

      if (!addressToDelete) {
        throw new NotFoundException('Address not found.');
      }

      // Store the address data before deletion
      const deletedAddressData = { ...addressToDelete };

      // Delete the address
      await queryRunner.manager.delete(Address, { id });

      // Reassign primary address if needed
      if (addressToDelete.is_primary) {
        const anotherAddress = await queryRunner.manager.findOne(Address, {
          where: { user_id: addressToDelete.user_id },
          order: { created_at: 'DESC' }
        });

        if (anotherAddress) {
          await queryRunner.manager.update(
            Address,
            { id: anotherAddress.id },
            { is_primary: true }
          );
        }
      }

      await queryRunner.commitTransaction();
      return deletedAddressData;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async setDefaultAddress(id: string, userId: string): Promise<Address> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // First, unset all primary addresses for this user
      await queryRunner.manager.update(
        Address,
        { user_id: userId },
        { is_primary: false }
      );

      // Then set the specified address as primary
      await queryRunner.manager.update(
        Address,
        { id, user_id: userId },
        { is_primary: true }
      );

      const updatedAddress = await queryRunner.manager.findOne(Address, {
        where: { id, user_id: userId }
      });

      if (!updatedAddress) {
        throw new NotFoundException(`Address not found.`);
      }

      await queryRunner.commitTransaction();
      return updatedAddress;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }
}
