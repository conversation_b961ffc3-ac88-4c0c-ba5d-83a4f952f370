import {
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { CreateAddressDto } from './dto/create-address.dto';
import { UpdateAddressDto } from './dto/update-address.dto';
import { InjectModel } from '@nestjs/mongoose';
import { Address, AddressDocument } from './schema';
import { Model } from 'mongoose';
import { UsersService } from 'src/users/users.service';
import { Types } from 'mongoose';
import {
  Buyer,
  BuyerDocument,
  Farmer,
  FarmerDocument,
} from 'src/users/user.schema';

@Injectable()
export class AddressesService {
  constructor(
    private readonly userService: UsersService,
    @InjectModel(Address.name)
    private readonly addressModel: Model<AddressDocument>,
    @InjectModel(Buyer.name)
    private readonly buyerModel: Model<BuyerDocument>,
    @InjectModel(Farmer.name)
    private readonly farmerModel: Model<FarmerDocument>,
  ) {}

  async create(createAddressDto: CreateAddressDto): Promise<Address> {
    const { userId, ...addressData } = createAddressDto;

    // Try to find user from Buyer or Farmer model
    const buyer = await this.buyerModel.findById(userId);
    const farmer = !buyer ? await this.farmerModel.findById(userId) : null;

    const user = buyer || farmer;
    if (!user) throw new NotFoundException('User not found');

    // Check for duplicate address for the same user
    const existingDuplicate = await this.addressModel.findOne({
      userId,
      houseNumber: addressData.houseNumber,
      streetName: addressData.streetName,
      lga: addressData.lga,
      state: addressData.state,
      community: addressData.community,
    });

    if (existingDuplicate) {
      throw new ConflictException('This address already exists for the user.');
    }

    // Check if this is the user's first address
    const isFirstAddress =
      (await this.addressModel.countDocuments({ userId })) === 0;

    const newAddress = new this.addressModel({
      ...createAddressDto,
      isDefault: isFirstAddress || createAddressDto.isDefault,
    });

    const savedNewAddress = await newAddress.save();

    // Add new address to the user's address list and save
    user.addresses = [...(user.addresses ?? []), savedNewAddress._id];
    await user.save();

    return savedNewAddress;
  }

  // async create(createAddressDto: CreateAddressDto): Promise<Address> {
  //   const { userId, ...addressData } = createAddressDto;

  //   // Check user existence across all user models
  //   const existingUser = await this.userService.findById(userId);
  //   if (!existingUser) throw new NotFoundException('User not found');

  //   const isFirstAddress =
  //     (await this.addressModel
  //       .countDocuments({ userId: createAddressDto.userId })
  //       .exec()) === 0;
  //   const newAddress = new this.addressModel({
  //     ...createAddressDto,
  //     isDefault: isFirstAddress || createAddressDto.isDefault,
  //   });
  //   const savedNewAddress = await newAddress.save();

  //   console.log(existingUser.user);

  //   return savedNewAddress;
  // }

  // existingUser.addresses.push(await savedNewAddress.id);
  // existingUser.save();

  async findAll(userId?: string): Promise<Address[]> {
    if (userId) {
      return await this.addressModel.find({ userId }).exec();
    }
    return await this.addressModel.find().exec();
  }

  async findOne(id: string): Promise<Address> {
    const address = await this.addressModel
      .findById(new Types.ObjectId(id))
      .exec();
    if (!address) {
      throw new NotFoundException(`Address not found.`);
    }
    return address;
  }

  async update(
    id: string,
    updateAddressDto: UpdateAddressDto,
  ): Promise<Address> {
    const session = await this.addressModel.db.startSession();
    session.startTransaction();

    try {
      const address = await this.addressModel.findById(id).exec();

      if (!address) {
        throw new NotFoundException('Address not found.');
      }

      // If this address is being marked as default, unset others
      if (updateAddressDto.isDefault) {
        await this.addressModel.updateMany(
          { userId: address.userId, _id: { $ne: id } },
          { $set: { isDefault: false } },
          { session },
        );
      }

      const updatedAddress = await this.addressModel
        .findByIdAndUpdate(id, updateAddressDto, {
          new: true,
          runValidators: true,
          session,
        })
        .exec();

      if (!updatedAddress) {
        throw new NotFoundException('Address not found after update.');
      }

      await session.commitTransaction();
      return updatedAddress;
    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  }

  // async remove(id: string): Promise<Address> {
  //   const session = await this.addressModel.db.startSession();
  //   session.startTransaction();

  //   try {
  //     const addressToDelete = await this.addressModel
  //       .findById(id)
  //       .session(session);

  //     if (!addressToDelete) {
  //       throw new NotFoundException('Address not found.');
  //     }

  //     const deletedAddress = await this.addressModel
  //       .findByIdAndDelete(id)
  //       .session(session)
  //       .exec();

  //     if (addressToDelete.isDefault) {
  //       // Check for another address by this user
  //       const anotherAddress = await this.addressModel
  //         .findOne({ userId: addressToDelete.userId })
  //         .sort({ createdAt: -1 }) // or any order you prefer
  //         .session(session)
  //         .exec();

  //       if (anotherAddress) {
  //         anotherAddress.isDefault = true;
  //         await anotherAddress.save({ session });
  //       }
  //     }

  //     await session.commitTransaction();
  //     return deletedAddress;
  //   } catch (error) {
  //     await session.abortTransaction();
  //     throw error;
  //   } finally {
  //     session.endSession();
  //   }
  // }

  async remove(id: string): Promise<Address> {
    const session = await this.addressModel.db.startSession();
    session.startTransaction();

    try {
      const addressToDelete = await this.addressModel
        .findById(id)
        .session(session);
      if (!addressToDelete) {
        throw new NotFoundException('Address not found.');
      }

      const deletedAddress = await this.addressModel
        .findByIdAndDelete(id)
        .session(session)
        .exec();

      // Try removing from Buyer
      await this.buyerModel.updateOne(
        { _id: addressToDelete.userId },
        { $pull: { addresses: addressToDelete._id } },
        { session },
      );

      // Try removing from Farmer
      await this.farmerModel.updateOne(
        { _id: addressToDelete.userId },
        { $pull: { addresses: addressToDelete._id } },
        { session },
      );

      // Reassign default address if needed
      if (addressToDelete.isDefault) {
        const anotherAddress = await this.addressModel
          .findOne({ userId: addressToDelete.userId })
          .sort({ createdAt: -1 })
          .session(session)
          .exec();

        if (anotherAddress) {
          anotherAddress.isDefault = true;
          await anotherAddress.save({ session });
        }
      }

      await session.commitTransaction();
      return deletedAddress;
    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  }

  async setDefaultAddress(id: string, userId: string): Promise<Address> {
    await this.addressModel.updateMany({ userId }, { isDefault: false }).exec();
    const updatedAddress = await this.addressModel
      .findByIdAndUpdate(
        new Types.ObjectId(id),
        { isDefault: true },
        { new: true, runValidators: true },
      )
      .exec();

    if (!updatedAddress) {
      throw new NotFoundException(`Address not found.`);
    }

    return updatedAddress;
  }
}
