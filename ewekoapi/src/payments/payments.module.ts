import { Module } from '@nestjs/common';
import { PaymentsService } from './payments.service';
import { PaymentsController } from './payments.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User, Order, Transaction } from '../users/entities/user.entity';
import { OrdersService } from 'src/orders/orders.service';
import { TransactionsService } from 'src/transactions/transactions.service';
import { UsersService } from 'src/users/users.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, Order, Transaction]),
  ],
  controllers: [PaymentsController],
  providers: [
    PaymentsService,
    OrdersService,
    TransactionsService,
    UsersService,
  ],
  exports: [PaymentsService, TypeOrmModule],
})
export class PaymentsModule {}

import { WalletsService } from 'src/wallets/wallets.service';
import {
  Admin,
  AdminSchema,
  Business,
  BusinessSchema,
  Buyer,
  BuyerSchema,
  Contact,
  ContactSchema,
  Farmer,
  FarmerSchema,
  FieldAgent,
  FieldAgentSchema,
  LogisticsAgentSchema,
  Profile,
  ProfileSchema,
  User,
  UserSchema,
} from 'src/users/user.schema';
import { UserType } from 'src/shared/enums';
import { CartService } from 'src/cart/cart.service';
import { PreferencesService } from 'src/preferences/preferences.service';
import { Cart, CartItem, CartItemSchema, CartSchema } from 'src/cart/schema';
import { Preferences, PreferencesSchema } from 'src/preferences/schema';
import { Notification, NotificationSchema } from 'src/notifications/schemas';

@Module({
  imports: [
    // Register User schema with discriminators
    MongooseModule.forFeatureAsync([
      {
        name: User.name,
        useFactory: () => {
          // Register discriminators using UserType enum values
          UserSchema.discriminator(UserType.BUYER, BuyerSchema);
          UserSchema.discriminator(UserType.FARMER, FarmerSchema);
          UserSchema.discriminator(UserType.AGENT, FieldAgentSchema);
          UserSchema.discriminator(
            UserType.LOGISTICS_AGENT,
            LogisticsAgentSchema,
          );
          UserSchema.discriminator(UserType.ADMIN, AdminSchema);

          return UserSchema;
        },
      },
    ]),
    MongooseModule.forFeature([
      { name: Produce.name, schema: ProduceSchema },
      { name: Category.name, schema: CategorySchema },
      { name: Order.name, schema: OrderSchema },
      { name: Buyer.name, schema: BuyerSchema },
      { name: Farmer.name, schema: FarmerSchema },
      { name: FieldAgent.name, schema: FieldAgentSchema },
      { name: Admin.name, schema: AdminSchema },
      { name: Transaction.name, schema: TransactionSchema },
      { name: Address.name, schema: AddressSchema },
      { name: Wallet.name, schema: WalletSchema },
      { name: OrderItem.name, schema: OrderItemSchema },
      { name: Profile.name, schema: ProfileSchema },
      { name: Contact.name, schema: ContactSchema },
      { name: Business.name, schema: BusinessSchema },
      { name: Cart.name, schema: CartSchema },
            { name: Preferences.name, schema: PreferencesSchema },
            { name: CartItem.name, schema: CartItemSchema },
            { name: Notification.name, schema: NotificationSchema },
    ]),
  ],
  controllers: [PaymentsController],
  providers: [
    PaymentsService,
    OrdersService,
    TransactionsService,
    UsersService,
    ProduceService,
    PaginationService,
    WalletsService, CartService, PreferencesService
  ],
})
export class PaymentsModule {}
