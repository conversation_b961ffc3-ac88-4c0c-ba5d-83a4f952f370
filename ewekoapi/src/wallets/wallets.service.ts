import {
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { CreateWalletDto } from './dto/create-wallet.dto';
import { UpdateWalletDto } from './dto/update-wallet.dto';
import { InjectModel } from '@nestjs/mongoose';
import { Wallet } from './schema';
import { Model } from 'mongoose';
// import { Farmer } from 'src/users/schemas';
import { Types } from 'mongoose';
import { Farmer } from 'src/users/user.schema';

@Injectable()
export class WalletsService {
  constructor(
    @InjectModel(Wallet.name) private walletModel: Model<Wallet>,
    @InjectModel(Farmer.name) private readonly farmerModel: Model<Farmer>,
  ) {}

  async create(createWalletDto: CreateWalletDto): Promise<Wallet> {
    const { farmer } = createWalletDto;

    if (!Types.ObjectId.isValid(farmer)) {
      throw new NotFoundException('Invalid farmer ID');
    }

    const farmerId = new Types.ObjectId(farmer);
    const existingFarmer = await this.farmerModel.findById(farmerId);

    if (!existingFarmer) {
      throw new NotFoundException('Farmer not found');
    }

    // Check if wallet already exists
    const existingWallet = await this.walletModel.findOne({ farmer: farmerId });
    if (existingWallet) {
      throw new NotFoundException('Farmer already has a wallet');
    }

    // Create new wallet
    const wallet = new this.walletModel({
      farmer: farmerId,
      balance: 0,
    });

    await wallet.save();
    return wallet;
  }

  // async findAll(): Promise<Wallet[]> {
  //   return this.walletModel.find().populate(['farmer']).exec();
  // }

  async findAll() {
    const wallets = await this.walletModel
      .find()
      // .populate('farmer', 'name email phoneNumber')
      .sort({ createdAt: -1 });

    return wallets;
  }

  async findByFarmer(farmerId: string): Promise<Wallet> {
    if (!Types.ObjectId.isValid(farmerId)) {
      throw new NotFoundException('Invalid farmer ID');
    }

    // First check if the farmer exists
    const farmerExists = await this.farmerModel.exists({
      _id: new Types.ObjectId(farmerId),
    });
    if (!farmerExists) {
      throw new NotFoundException('Farmer not found');
    }

    const wallet = await this.walletModel
      .findOne({ farmer: new Types.ObjectId(farmerId) })
      .populate('farmer', 'name email phoneNumber');

    if (!wallet) {
      throw new NotFoundException('Wallet not found for this farmer');
    }

    return wallet;
  }

  async findOne(id: string): Promise<Wallet> {
    if (!Types.ObjectId.isValid(id)) {
      throw new NotFoundException('Invalid wallet ID');
    }

    const wallet = await this.walletModel.findById(id);

    if (!wallet) {
      throw new NotFoundException('Wallet not found');
    }

    return wallet;
  }

  async update(id: string, updateWalletDto: UpdateWalletDto) {
    if (!Types.ObjectId.isValid(id))
      throw new NotFoundException('Invalid wallet ID');

    const updatedWallet = await this.walletModel
      .findByIdAndUpdate(new Types.ObjectId(id), updateWalletDto, {
        new: true,
        runValidators: true,
      })
      .exec();

    if (!updatedWallet) throw new NotFoundException('Wallet not found');

    return updatedWallet;
  }

  async remove(id: string): Promise<void> {
    if (!Types.ObjectId.isValid(id))
      throw new NotFoundException('Invalid wallet ID');

    const deletedWallet = await this.walletModel
      .findByIdAndDelete(new Types.ObjectId(id))
      .exec();
    if (!deletedWallet) throw new NotFoundException('Wallet not found');
  }
}
