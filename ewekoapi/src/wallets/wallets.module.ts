import { Module } from '@nestjs/common';
import { WalletsService } from './wallets.service';
import { WalletsController } from './wallets.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { Wallet, WalletSchema } from './schema';
import {
  AdminSchema,
  BuyerSchema,
  Farmer,
  FarmerSchema,
  FieldAgentSchema,
  LogisticsAgentSchema,
  User,
  UserSchema,
} from 'src/users/user.schema';
import { UserType } from 'src/shared/enums';

@Module({
  imports: [
    // Register User schema with discriminators
    MongooseModule.forFeatureAsync([
      {
        name: User.name,
        useFactory: () => {
          // Register discriminators using UserType enum values
          UserSchema.discriminator(UserType.BUYER, BuyerSchema);
          UserSchema.discriminator(UserType.FARMER, FarmerSchema);
          UserSchema.discriminator(UserType.AGENT, FieldAgentSchema);
          UserSchema.discriminator(
            UserType.LOGISTICS_AGENT,
            LogisticsAgentSchema,
          );
          UserSchema.discriminator(UserType.ADMIN, AdminSchema);

          return UserSchema;
        },
      },
    ]),
    MongooseModule.forFeature([
      { name: Wallet.name, schema: WalletSchema },
      { name: Farmer.name, schema: FarmerSchema },
    ]),
  ],
  controllers: [WalletsController],
  providers: [WalletsService],
})
export class WalletsModule {}
