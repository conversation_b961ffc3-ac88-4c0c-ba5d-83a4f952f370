import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Types, HydratedDocument } from 'mongoose';

export type WalletDocument = HydratedDocument<Wallet>;

@Schema({ timestamps: true })
export class Wallet {
  @Prop({ type: Types.ObjectId, ref: 'Farmer', required: true })
  farmer: Types.ObjectId;

  @Prop({ type: Number, required: true, default: 0 })
  balance: number;

  @Prop({ type: Number, required: true, default: 0 })
  grossRevenue: number;
}

export const WalletSchema = SchemaFactory.createForClass(Wallet);
