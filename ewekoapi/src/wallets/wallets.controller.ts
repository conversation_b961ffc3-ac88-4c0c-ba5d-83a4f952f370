import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
} from '@nestjs/common';
import { WalletsService } from './wallets.service';
import { CreateWalletDto } from './dto/create-wallet.dto';
import { UpdateWalletDto } from './dto/update-wallet.dto';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { CurrentUser } from 'src/shared/decorators/currentUser.decorator';
import { Wallet } from './schema';

@Controller('wallets')
@ApiTags('wallets')
export class WalletsController {
  constructor(private readonly walletsService: WalletsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new wallet' })
  @ApiResponse({
    status: 201,
    description: 'Wallet created successfully',
    type: Wallet,
  })
  @ApiResponse({
    status: 409,
    description: 'Wallet already exists for this farmer',
  })
  @ApiResponse({ status: 404, description: 'Farmer not found' })
  create(@Body() createWalletDto: CreateWalletDto) {
    return this.walletsService.create(createWalletDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all wallets' })
  @ApiResponse({
    status: 200,
    description: 'Returns all wallets',
    type: [Wallet],
  })
  findAll(): Promise<Wallet[]> {
    return this.walletsService.findAll();
  }

  @Get('/me')
  @ApiOperation({ summary: 'Get authenticated farmer wallet' })
  @ApiResponse({
    status: 200,
    description: 'Returns the authenticated farmer wallet',
    type: Wallet,
  })
  @ApiResponse({ status: 404, description: 'Wallet not found' })
  getWallet(@CurrentUser('id') userId: string): Promise<Wallet | null> {
    return this.walletsService.findByFarmer(userId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a wallet by ID' })
  @ApiResponse({
    status: 200,
    description: 'Returns the requested wallet',
    type: Wallet,
  })
  @ApiResponse({ status: 404, description: 'Wallet not found' })
  findOne(@Param('id') id: string) {
    return this.walletsService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a wallet by ID' })
  @ApiResponse({
    status: 200,
    description: 'Wallet updated successfully',
    type: Wallet,
  })
  @ApiResponse({ status: 404, description: 'Wallet not found' })
  update(@Param('id') id: string, @Body() updateWalletDto: UpdateWalletDto) {
    return this.walletsService.update(id, updateWalletDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a wallet by ID' })
  @ApiResponse({ status: 200, description: 'Wallet deleted successfully' })
  @ApiResponse({ status: 404, description: 'Wallet not found' })
  remove(@Param('id') id: string): Promise<void> {
    return this.walletsService.remove(id);
  }
}
