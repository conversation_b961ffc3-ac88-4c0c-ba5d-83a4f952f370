import {
  BadRequestException,
  ConflictException,
  HttpStatus,
  Injectable,
  NotFoundException,
  Res,
  UnauthorizedException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import parsePhoneNumberFromString from 'libphonenumber-js';
import {
  // CreateAdminDto,
  // CreateAgentDto,
  CreateBuyerDto,
  CreateFarmerDto,
  // CreateFarmerDto,
} from 'src/users/dto/create-user.dto';
import { UsersService } from 'src/users/users.service';
import * as bcrypt from 'bcryptjs';
import { OtpsService } from 'src/otps/otps.service';
import {
  AdminRole,
  NotificationDestination,
  OtpUseCase,
  UserType,
} from 'src/shared/enums';
import { VerifyOtpDto } from 'src/otps/dto/update-otp.dto';
import { Model, Types } from 'mongoose';
import { isBefore } from 'date-fns';
import {
  LoginDto,
  ResetPasswordDto,
  ResetPasswordEmailDto,
  VerifyAdminLoginDto,
  VerifyLoginDto,
} from './dto/dtos';
import { Response as ExpressResponse } from 'express';
import { InjectModel } from '@nestjs/mongoose';
import { AdminToken, AdminTokenDocument } from 'src/admin-tokens/schema';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { CartService } from 'src/cart/cart.service';
import { Wallet, WalletDocument } from 'src/wallets/schema';
import { Preferences, PreferencesDocument } from 'src/preferences/schema';
import { PreferencesService } from 'src/preferences/preferences.service';
import {
  Contact,
  ContactDocument,
  Profile,
  ProfileDocument,
} from 'src/users/user.schema';
import { WalletsService } from 'src/wallets/wallets.service';

@Injectable()
export class AuthService {
  constructor(
    private readonly configService: ConfigService,
    private readonly usersService: UsersService,
    private readonly jwtService: JwtService,
    private readonly otpService: OtpsService,
    private readonly cartService: CartService,
    private readonly walletService: WalletsService,
    private readonly preferenceService: PreferencesService,
    private eventEmitter: EventEmitter2,
    // @InjectModel(AdminToken.name)
    // private readonly adminTokenModel: Model<AdminTokenDocument>,
    // @InjectModel(Wallet.name)
    // private readonly walletModel: Model<WalletDocument>,
    // @InjectModel(Preferences.name)
    // private readonly preferenceModel: Model<PreferencesDocument>,
    @InjectModel(Contact.name)
    private readonly contactModel: Model<ContactDocument>,
    @InjectModel(Profile.name)
    private readonly profileModel: Model<ProfileDocument>,
    @InjectModel(Preferences.name)
    private readonly preferenceModel: Model<PreferencesDocument>,
  ) {}

  async signupBuyer(signUpDto: CreateBuyerDto) {
    const phoneNumber = parsePhoneNumberFromString(signUpDto.primaryPhone);
    if (!phoneNumber || !phoneNumber.isValid()) {
      throw new BadRequestException('Invalid phone number.');
    }

    const emailExists = await this.usersService.findUserByEmail(
      signUpDto.email,
    );
    const phoneExists = await this.usersService.findUserByPhone(
      signUpDto.primaryPhone,
    );
    const usernameExists = await this.usersService.findUserByUsername(
      signUpDto.primaryPhone,
    );

    if (emailExists) {
      throw new ConflictException(
        `Email already exists, please login to your account or try a new one`,
      );
    } else if (phoneExists) {
      throw new ConflictException(
        `Phone Number already exists, please login to your account or try a new one`,
      );
    } else if (usernameExists) {
      throw new ConflictException(
        `Username already exists, please login to your account or try a new one`,
      );
    }

    const signUpBuyer = await this.usersService.createBuyer(signUpDto);

    if (signUpBuyer._id) {
      await this.cartService.getOrCreateCart(signUpBuyer._id.toString());
      await this.preferenceService.create({ user: signUpBuyer._id.toString() });

      this.eventEmitter.emit('user.created', {
        ...signUpBuyer,
        firstName: signUpDto.firstName,
        userType: UserType.BUYER,
      });
    }

    return signUpBuyer;
  }

  async signupFarmer(signUpDto: CreateFarmerDto) {
    const phoneNumber = parsePhoneNumberFromString(signUpDto.primaryPhone);
    if (!phoneNumber || !phoneNumber.isValid()) {
      throw new BadRequestException('Invalid phone number.');
    }

    const emailExists = await this.usersService.findUserByEmail(
      signUpDto.email,
    );
    const phoneExists = await this.usersService.findUserByPhone(
      signUpDto.primaryPhone,
    );
    const usernameExists = await this.usersService.findUserByUsername(
      signUpDto.primaryPhone,
    );

    if (emailExists) {
      throw new ConflictException(
        `Email already exists, please login to your account or try a new one`,
      );
    } else if (phoneExists) {
      throw new ConflictException(
        `Phone Number already exists, please login to your account or try a new one`,
      );
    } else if (usernameExists) {
      throw new ConflictException(
        `Username already exists, please login to your account or try a new one`,
      );
    }

    const signUpFarmer = await this.usersService.createFarmer(signUpDto);

    if (signUpFarmer._id) {
      await this.walletService.create({ farmer: signUpFarmer._id });
      await this.preferenceService.create({
        user: signUpFarmer._id.toString(),
      });

      this.eventEmitter.emit('user.created', {
        ...signUpFarmer,
        firstName: signUpDto.firstName,
        userType: UserType.FARMER,
      });
    }

    return signUpFarmer;
  }

  async sendVerifyEmail(email: string) {
    const emailExists = await this.usersService.findUserByEmail(email);
    if (!emailExists) {
      throw new BadRequestException('Email does not exist');
    }
    const contact = await this.contactModel.findById(emailExists.user.contact);
    const profile = await this.profileModel.findById(emailExists.user.profile);
    const { otpDestination } = await this.preferenceModel.findOne({
      user: emailExists.user._id,
    });

    const createOtp = await this.otpService.create({
      useCase: OtpUseCase.VRFY_EMAIL,
      userId: emailExists.user._id.toString(),
    });

    // Send email with OTP
    if (createOtp.code) {
      this.eventEmitter.emit('verify.user', {
        ...createOtp,
        destination: NotificationDestination.EMAIL,
        id: emailExists.user._id.toString(),
        firstName: profile.firstName,
        email: contact.email,
        phoneNumber: contact.primaryPhone,
        userType: emailExists.userType,
      });
    }

    return createOtp;
  }

  async sendVerifyPhone(phoneNumber: string) {
    const phoneExists = await this.usersService.findUserByPhone(phoneNumber);
    if (!phoneExists) {
      throw new BadRequestException('Phone number does not exist');
    }
    const contact = await this.contactModel.findById(phoneExists.user.contact);

    const createOtp = await this.otpService.create({
      useCase: OtpUseCase.VRFY_PHONE,
      userId: phoneExists.user._id.toString(),
    });

    // Send SMS with OTP
    if (createOtp.code) {
      this.eventEmitter.emit('verify.user', {
        ...createOtp,
        destination: NotificationDestination.SMS,
        id: phoneExists.user._id,
        email: contact.email,
        phoneNumber: contact.primaryPhone,
        userType: phoneExists.userType,
      });
    }
    return createOtp;
  }

  async verifyOtp(code: string) {
    console.log('===================');
    console.log(code);
    console.log('===================');

    const otpEntry = await this.otpService.findByCode(code);
    if (!otpEntry || otpEntry.code !== code) {
      throw new BadRequestException('Invalid OTP.');
    }
    const existingUser = await this.usersService.findById(
      otpEntry.userId.toString(),
    );
    if (!existingUser) {
      throw new BadRequestException('User with the provided OTP is not found.');
    }

    const currentTime = new Date();
    if (isBefore(otpEntry.expiresAt, currentTime)) {
      // await this.otpService.remove(otpEntry._id.toString());
      throw new BadRequestException('OTP has expired.');
    }

    console.log(otpEntry);
    console.log(existingUser.user);

    if (otpEntry.userId.toString() === existingUser.user._id.toString()) {
      if (otpEntry.useCase === OtpUseCase.VRFY_EMAIL) {
        if (existingUser.user.type === UserType.BUYER) {
          const verified = await this.usersService.updateBuyer(
            existingUser.user._id.toString(),
            {
              verified: true,
            },
          );

          if (verified.verified === true) {
            return {
              message: 'Account verified successfully',
              userId: existingUser.user._id,
            };
          }
        }
        if (existingUser.user.type === UserType.FARMER) {
          const verified = await this.usersService.updateFarmer(
            existingUser.user._id.toString(),
            {
              verified: true,
            },
          );

          if (verified.verified === true) {
            return {
              message: 'Account verified successfully',
              userId: existingUser.user._id,
            };
          }
        }

        return {
          message: 'Account verified successfully',
          userId: existingUser.user._id,
        };
      }

      else if (otpEntry.useCase === OtpUseCase.VRFY_PHONE) {
        if (existingUser.user.type === UserType.BUYER) {
          const verified = await this.usersService.updateBuyer(
            existingUser.user._id.toString(),
            {
              verified: true,
            },
          );

          if (verified.verified === true) {
            return {
              message: 'Account verified successfully',
              userId: existingUser.user._id,
            };
          }
        }
        if (existingUser.user.type === UserType.FARMER) {
          const verified = await this.usersService.updateFarmer(
            existingUser.user._id.toString(),
            {
              verified: true,
            },
          );

          if (verified.verified === true) {
            return {
              message: 'Account verified successfully',
              userId: existingUser.user._id,
            };
          }
        }

        return {
          message: 'Account verified successfully',
          userId: existingUser.user._id,
        };
      //   const verified = await this.usersService.update(existingUser.id, {
      //     verified: true,
      //     isPhoneVerified: true,
      //   });

      //   if (verified.verified === true) {
      //     // await this.otpService.remove(otpEntry._id.toString());
      //     return {
      //       message: 'Account verified successfully',
      //       userId: existingUser.id,
      //     };
      //   }
      //   return {
      //     message: 'Account phone number verified successfully',
      //     userId: existingUser.id,
      //   };
      }
      else {
        // await this.otpService.update(otpEntry., { isUsed: true });

        return {
          message: 'OTP has been used successfully',
          userId: existingUser.user._id,
        };
      }
    } else {
      throw new BadRequestException(
        'OTP and user mismatched! Please try again.',
      );
      // return {
      //   message: '.',
      //   userId: existingUser.user._id,
      // };
    }
  }

  async sendPasswordResetEmail(email: string) {
    const emailExists = await this.usersService.findUserByEmail(email);
    if (!emailExists) {
      throw new BadRequestException('Email does not exist');
    }

    let frontendUrl = this.configService.getOrThrow<string>('FRONTEND_URL');
    const payload = { id: emailExists.user._id };
    const jwtSecret = this.configService.getOrThrow('JWT_SECRET');

    const token = await this.jwtService.signAsync(payload, {
      secret: jwtSecret,
      expiresIn: '5m',
    });

    const resetLink = `${frontendUrl}/auth/reset-password?token=${token}`;
    const contact = await this.contactModel.findById(emailExists.user.contact);
    const profile = await this.profileModel.findById(emailExists.user.profile);

    console.log('===================');
    console.log(resetLink);
    console.log('===================');

    if (resetLink) {
      this.eventEmitter.emit('forgot-password.email', {
        resetLink,
        destination: NotificationDestination.EMAIL,
        id: emailExists.user._id,
        email: contact.email,
        phoneNumber: contact.primaryPhone,
        firstName: profile.firstName,
        userType: emailExists.userType,
      });
    }

    return { message: 'Password reset link has been sent to your email.' };
  }

  async sendPasswordResetPhone(phoneNumber: string) {
    const phoneExists = await this.usersService.findUserByPhone(phoneNumber);
    if (!phoneExists) {
      throw new BadRequestException('Phone number does not exist');
    }

    const createOtp = await this.otpService.create({
      useCase: OtpUseCase.PWDR,
      userId: phoneExists.user._id.toString(),
    });

    const contact = await this.contactModel.findById(phoneExists.user.contact);
    const profile = await this.profileModel.findById(phoneExists.user.profile);

    if (createOtp.code) {
      this.eventEmitter.emit('forgot-password.phone', {
        ...createOtp,
        destination: NotificationDestination.SMS,
        id: phoneExists.user._id,
        email: contact.email,
        phoneNumber: contact.primaryPhone,
        firstName: profile.firstName,
        userType: phoneExists.userType,
      });
    }

    return {
      message: 'Password reset otp has been sent to your phone number.',
    };
  }

  async resetPasswordEmail(
    token: string,
    resetPasswordDto: ResetPasswordEmailDto,
  ) {
    const { newPassword, confirmNewPassword } = resetPasswordDto;

    if (newPassword !== confirmNewPassword) {
      throw new BadRequestException('Passwords do not match.');
    }

    let payload;
    try {
      payload = this.jwtService.verify(token, {
        secret: this.configService.getOrThrow('JWT_SECRET'),
      });
    } catch (e) {
      throw new UnauthorizedException('Invalid or expired token.');
    }

    const user = await this.usersService.findById(payload.id);
    if (!user) {
      throw new BadRequestException('User not found.');
    }

    if (payload.id === user.user._id.toString()) {
      if (user.user.type === UserType.BUYER) {
        await this.usersService.updateBuyer(user.user._id.toString(), {
          password: newPassword,
        });
        return { message: 'Password has been reset successfully.' };
      }
      if (user.user.type === UserType.FARMER) {
        await this.usersService.updateFarmer(user.user._id.toString(), {
          password: newPassword,
        });
        return { message: 'Password has been reset successfully.' };
      }
    } else {
      throw new BadRequestException(
        'Password reset via email failed. Please try again',
      );
    }
  }

  async changePassword(
    userId: string,
    resetPasswordDto: ResetPasswordEmailDto,
  ) {
    const { newPassword, confirmNewPassword } = resetPasswordDto;

    if (newPassword !== confirmNewPassword) {
      throw new BadRequestException('Passwords do not match.');
    }

    const user = await this.usersService.findById(userId);
    if (!user) {
      throw new BadRequestException('User not found.');
    }

    if (user.user.type === UserType.BUYER) {
      await this.usersService.updateBuyer(user.user._id.toString(), {
        password: newPassword,
      });
      return { message: 'Password changed successfully' };
    }
    if (user.user.type === UserType.FARMER) {
      await this.usersService.updateFarmer(user.user._id.toString(), {
        password: newPassword,
      });
      return { message: 'Password changed successfully' };
    }
  }

  async resetPasswordPhone(resetPasswordDto: ResetPasswordDto) {
    const { otp, newPassword, confirmNewPassword } = resetPasswordDto;

    if (newPassword !== confirmNewPassword) {
      throw new BadRequestException('Passwords do not match.');
    }

    const verifiedOtp = await this.verifyOtp(otp);

    const existingUser = await this.usersService.findById(
      verifiedOtp.userId.toString(),
    );

    if (!existingUser) {
      throw new BadRequestException('User with the provided OTP is not found.');
    }

    if (verifiedOtp.message) {
      if (existingUser.user.type === UserType.BUYER) {
        await this.usersService.updateBuyer(existingUser.user._id.toString(), {
          password: newPassword,
        });
        return { message: 'Password has been reset successfully' };
      }
      if (existingUser.user.type === UserType.FARMER) {
        await this.usersService.updateFarmer(existingUser.user._id.toString(), {
          password: newPassword,
        });
        return { message: 'Password has been reset successfully' };
      }
    }
  }

  async login(loginDto: LoginDto) {
    const { email, password } = loginDto;

    const user = await this.usersService.findUserByEmail(email);
    if (!user) throw new NotFoundException('User not found');

    const contact = await this.contactModel.findById(user.user.contact);
    const profile = await this.profileModel.findById(user.user.profile);

    if (!user.user.verified) {
      return {
        message: 'Please verify your account first.',
        data: { email: contact.email, phoneNumber: contact.primaryPhone },
      };
    }

    const isPasswordValid = await bcrypt.compare(password, user.user.password);
    if (!isPasswordValid) {
      throw new ConflictException('Invalid password.');
    }

    const payload = {
      id: user.user._id.toString(),
      firstName: profile.firstName,
      lastName: profile.lastName,
      email: contact.email,
      phone: contact.primaryPhone,
      userType: user.user.type,
      profilePicture: profile.profilePicture
        ? profile.profilePicture
        : 'https://t4.ftcdn.net/jpg/06/30/06/81/360_F_630068155_RnZI6mC91wz7gUYFVmhzwpl4O6x00Cbh.jpg',
    };

    const token = await this.signToken(payload);

    if (token) {
      if (user.user.type === UserType.BUYER) {
        await this.usersService.updateBuyer(user.user._id.toString(), {
          lastLogin: new Date(),
        });
      }
      if (user.user.type === UserType.FARMER) {
        await this.usersService.updateFarmer(user.user._id.toString(), {
          lastLogin: new Date(),
        });
      }
      return {
        message: 'Logged in successfully',
        userType: user.user.type,
        token,
      };
    }

    // Check user's OTP delivery preference
    // Send OTP via email or phone number
    // const sendOtp = await this.send2FACode(user.email);

    // console.log('-------------------------------');
    // console.log('Send OTP response', sendOtp);
    // console.log('-------------------------------');

    // if (sendOtp.message.includes('successfully)) {
    //   if (user.user.type === UserType.ADMIN) {
    //     // return { adminToken: user.adminToken };
    //   } else {
    //     return { message: sendOtp.message, userId: user.id };
    //   }
    //   return { message: sendOtp.message, userId: user.id };
    // } else {
    //   throw new BadRequestException('Failed to send OTP. Please try again.');
    // }
  }

  async verifyLogin(verifyLoginDto: VerifyLoginDto) {
    const user = await this.usersService.findById(verifyLoginDto.userId);
    if (!user) throw new NotFoundException('User not found');

    console.log('Verify login user', user);

    const otpVerified = await this.verifyOtp(verifyLoginDto.otp);
    const contact = await this.contactModel.findById(user.user.contact);
    const profile = await this.profileModel.findById(user.user.profile);

    if (otpVerified.userId === user.user._id) {
      const payload = {
        id: user.user._id.toString(),
        firstName: profile.firstName,
        lastName: profile.lastName,
        email: contact.email,
        phone: contact.primaryPhone,
        userType: user.user.type,
        profilePicture: profile.profilePicture
          ? profile.profilePicture
          : 'https://t4.ftcdn.net/jpg/06/30/06/81/360_F_630068155_RnZI6mC91wz7gUYFVmhzwpl4O6x00Cbh.jpg',
      };

      const token = await this.signToken(payload);

      if (token) {
        user.user.lastLogin = new Date();
        user.user.save();
        return {
          message: 'Logged in successfully',
          userType: user.user.type,
          token,
        };
      }
    }
  }

  async send2FACode(emailOrPhone: string) {
    const emailExists = await this.usersService.findUserByEmail(emailOrPhone);
    const phoneExists = await this.usersService.findUserByPhone(emailOrPhone);

    if (!emailExists && !phoneExists) {
      throw new NotFoundException(`User not found`);
    }

    let createOtp: any;
    let destination: any;

    if (emailExists) {
      createOtp = await this.otpService.create({
        useCase: OtpUseCase['2FA'],
        userId: emailExists.user._id.toString(),
      });
      destination = NotificationDestination.EMAIL;
    }

    if (phoneExists) {
      createOtp = await this.otpService.create({
        useCase: OtpUseCase['2FA'],
        userId: phoneExists.user._id.toString(),
      });
      destination = NotificationDestination.SMS;
    }

    if (emailExists) {
      const contact = await this.contactModel.findById(
        emailExists.user.contact,
      );
      const profile = await this.profileModel.findById(
        emailExists.user.profile,
      );
      this.eventEmitter.emit('verify.2fa', {
        ...createOtp,
        destination,
        id: emailExists.user._id.toString(),
        email: contact.email,
        phoneNumber: contact.primaryPhone,
        userType: emailExists.userType,
        firstName: profile.firstName,
      });
    } else if (phoneExists) {
      const contact = await this.contactModel.findById(
        emailExists.user.contact,
      );
      const profile = await this.profileModel.findById(
        emailExists.user.profile,
      );

      this.eventEmitter.emit('verify.2fa', {
        ...createOtp,
        destination,
        id: phoneExists.user._id.toString(),
        email: contact.email,
        phoneNumber: contact.primaryPhone,
        firstName: profile.firstName,
        userType: phoneExists.userType,
      });
    }

    return {
      message: '2FA code sent successfully',
      createOtp,
    };
  }

  // async verifyAdminLogin(
  //   verifyLoginDto: VerifyAdminLoginDto,
  //   @Res({ passthrough: true }) res: ExpressResponse,
  // ) {
  //   const token = await this.adminTokenModel.findOne({
  //     token: verifyLoginDto.adminToken,
  //   });

  //   const user = await this.usersService.findByEmail(token.adminEmail);
  //   if (!user) throw new NotFoundException('Admin not found');

  //   const otpVerified = await this.verifyOtp(verifyLoginDto.otp);

  //   if (otpVerified.message.includes('successfully)) {
  //     const payload = {
  //       id: user.id,
  //       name: user.firstName,
  //       email: user.email,
  //       phone: user.phoneNumber,
  //       userType: user.user.type,
  //     };

  //     const token = await this.signToken(payload);

  //     console.log(token);

  //     if (token) {
  //       // Set the JWT token as an HTTP-only cookie
  //       res.cookie('3w3k0AuthToken', token, {
  //         httpOnly: true,
  //         secure: process.env.NODE_ENV === 'production',
  //         maxAge: 3600000,
  //         sameSite: 'strict', // Protects against CSRF attacks
  //       });
  //       return { message: 'Logged in successfully' };
  //     }
  //   }

  private async signToken(args: { id: string; userType: UserType }) {
    const payload = args;
    const jwtSecret = this.configService.getOrThrow('JWT_SECRET');

    return await this.jwtService.signAsync(payload, { secret: jwtSecret });
  }
}
