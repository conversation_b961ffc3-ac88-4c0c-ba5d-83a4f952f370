import {
  BadRequestException,
  ConflictException,
  HttpStatus,
  Injectable,
  NotFoundException,
  Res,
  UnauthorizedException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import parsePhoneNumberFromString from 'libphonenumber-js';
import {
  // CreateAdminDto,
  // CreateAgentDto,
  CreateBuyerDto,
  CreateFarmerDto,
  // CreateFarmerDto,
} from 'src/users/dto/create-user.dto';
import { UsersService } from 'src/users/users.service';
import * as bcrypt from 'bcryptjs';
import { OtpsService } from 'src/otps/otps.service';
import {
  AdminRole,
  NotificationDestination,
  OtpUseCase,
  UserType,
} from 'src/shared/enums';
import { VerifyOtpDto } from 'src/otps/dto/update-otp.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { isBefore } from 'date-fns';
import {
  LoginDto,
  ResetPasswordDto,
  ResetPasswordEmailDto,
  VerifyAdminLoginDto,
  VerifyLoginDto,
} from './dto/dtos';
import { Response as ExpressResponse } from 'express';
import { User, PhoneNumber } from 'src/users/entities/user.entity';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { CartService } from 'src/cart/cart.service';
import { PreferencesService } from 'src/preferences/preferences.service';
import { WalletsService } from 'src/wallets/wallets.service';

@Injectable()
export class AuthService {
  constructor(
    private readonly configService: ConfigService,
    private readonly usersService: UsersService,
    private readonly jwtService: JwtService,
    private readonly otpService: OtpsService,
    private readonly cartService: CartService,
    private readonly walletService: WalletsService,
    private readonly preferenceService: PreferencesService,
    private eventEmitter: EventEmitter2,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(PhoneNumber)
    private readonly phoneNumberRepository: Repository<PhoneNumber>,
  ) {}

  async signupBuyer(signUpDto: CreateBuyerDto) {
    const phoneNumber = parsePhoneNumberFromString(signUpDto.primaryPhone);
    if (!phoneNumber || !phoneNumber.isValid()) {
      throw new BadRequestException('Invalid phone number.');
    }

    const emailExists = await this.usersService.findUserByEmail(
      signUpDto.email,
    );
    const phoneExists = await this.usersService.findUserByPhone(
      signUpDto.primaryPhone,
    );
    const usernameExists = await this.usersService.findUserByUsername(
      signUpDto.primaryPhone,
    );

    if (emailExists) {
      throw new ConflictException(
        `Email already exists, please login to your account or try a new one`,
      );
    } else if (phoneExists) {
      throw new ConflictException(
        `Phone Number already exists, please login to your account or try a new one`,
      );
    } else if (usernameExists) {
      throw new ConflictException(
        `Username already exists, please login to your account or try a new one`,
      );
    }

    const signUpBuyer = await this.usersService.createBuyer(signUpDto);

    if (signUpBuyer.id) {
      await this.cartService.getOrCreateCart(signUpBuyer.id);
      await this.preferenceService.create({ user: signUpBuyer.id });

      this.eventEmitter.emit('user.created', {
        ...signUpBuyer,
        firstName: signUpDto.firstName,
        userType: UserType.BUYER,
      });
    }

    return signUpBuyer;
  }

  async signupFarmer(signUpDto: CreateFarmerDto) {
    const phoneNumber = parsePhoneNumberFromString(signUpDto.primaryPhone);
    if (!phoneNumber || !phoneNumber.isValid()) {
      throw new BadRequestException('Invalid phone number.');
    }

    const emailExists = await this.usersService.findUserByEmail(
      signUpDto.email,
    );
    const phoneExists = await this.usersService.findUserByPhone(
      signUpDto.primaryPhone,
    );
    const usernameExists = await this.usersService.findUserByUsername(
      signUpDto.primaryPhone,
    );

    if (emailExists) {
      throw new ConflictException(
        `Email already exists, please login to your account or try a new one`,
      );
    } else if (phoneExists) {
      throw new ConflictException(
        `Phone Number already exists, please login to your account or try a new one`,
      );
    } else if (usernameExists) {
      throw new ConflictException(
        `Username already exists, please login to your account or try a new one`,
      );
    }

    const signUpFarmer = await this.usersService.createFarmer(signUpDto);

    if (signUpFarmer.id) {
      await this.walletService.create({ farmer: signUpFarmer.id });
      await this.preferenceService.create({
        user: signUpFarmer.id,
      });

      this.eventEmitter.emit('user.created', {
        ...signUpFarmer,
        firstName: signUpDto.firstName,
        userType: UserType.FARMER,
      });
    }

    return signUpFarmer;
  }

  async sendVerifyEmail(email: string) {
    const user = await this.usersService.findUserByEmail(email);
    if (!user) {
      throw new BadRequestException('Email does not exist');
    }

    const createOtp = await this.otpService.create({
      useCase: OtpUseCase.VRFY_EMAIL,
      userId: user.id,
    });

    // Send email with OTP
    if (createOtp.code) {
      // Get primary phone number
      const primaryPhone = await this.phoneNumberRepository.findOne({
        where: { user_id: user.id, is_primary: true }
      });

      this.eventEmitter.emit('verify.user', {
        ...createOtp,
        destination: NotificationDestination.EMAIL,
        id: user.id,
        firstName: user.first_name,
        email: user.email,
        phoneNumber: primaryPhone?.phone_number || '',
        userType: user.type,
      });
    }

    return createOtp;
  }

  async sendVerifyPhone(phoneNumber: string) {
    const user = await this.usersService.findUserByPhone(phoneNumber);
    if (!user) {
      throw new BadRequestException('Phone number does not exist');
    }

    const createOtp = await this.otpService.create({
      useCase: OtpUseCase.VRFY_PHONE,
      userId: user.id,
    });

    // Send SMS with OTP
    if (createOtp.code) {
      this.eventEmitter.emit('verify.user', {
        ...createOtp,
        destination: NotificationDestination.SMS,
        id: user.id,
        email: user.email,
        phoneNumber: phoneNumber,
        userType: user.type,
      });
    }
    return createOtp;
  }

  async verifyOtp(code: string) {
    console.log('===================');
    console.log(code);
    console.log('===================');

    const otpEntry = await this.otpService.findByCode(code);
    if (!otpEntry || otpEntry.code !== code) {
      throw new BadRequestException('Invalid OTP.');
    }
    const existingUser = await this.usersService.findById(
      otpEntry.userId.toString(),
    );
    if (!existingUser) {
      throw new BadRequestException('User with the provided OTP is not found.');
    }

    const currentTime = new Date();
    if (isBefore(otpEntry.expiresAt, currentTime)) {
      // await this.otpService.remove(otpEntry._id.toString());
      throw new BadRequestException('OTP has expired.');
    }

    console.log(otpEntry);
    console.log(existingUser);

    if (otpEntry.userId.toString() === existingUser.id) {
      if (otpEntry.useCase === OtpUseCase.VRFY_EMAIL) {
        if (existingUser.type === UserType.BUYER) {
          const verified = await this.usersService.updateBuyer(
            existingUser.id,
            {
              verified: true,
            },
          );

          if (verified.verified === true) {
            return {
              message: 'Account verified successfully',
              userId: existingUser.id,
            };
          }
        }
        if (existingUser.type === UserType.FARMER) {
          const verified = await this.usersService.updateFarmer(
            existingUser.id,
            {
              verified: true,
            },
          );

          if (verified.verified === true) {
            return {
              message: 'Account verified successfully',
              userId: existingUser.id,
            };
          }
        }

        return {
          message: 'Account verified successfully',
          userId: existingUser.id,
        };
      }

      else if (otpEntry.useCase === OtpUseCase.VRFY_PHONE) {
        if (existingUser.type === UserType.BUYER) {
          const verified = await this.usersService.updateBuyer(
            existingUser.id,
            {
              verified: true,
            },
          );

          if (verified.verified === true) {
            return {
              message: 'Account verified successfully',
              userId: existingUser.id,
            };
          }
        }
        if (existingUser.type === UserType.FARMER) {
          const verified = await this.usersService.updateFarmer(
            existingUser.id,
            {
              verified: true,
            },
          );

          if (verified.verified === true) {
            return {
              message: 'Account verified successfully',
              userId: existingUser.id,
            };
          }
        }

        return {
          message: 'Account verified successfully',
          userId: existingUser.id,
        };
      //   const verified = await this.usersService.update(existingUser.id, {
      //     verified: true,
      //     isPhoneVerified: true,
      //   });

      //   if (verified.verified === true) {
      //     // await this.otpService.remove(otpEntry._id.toString());
      //     return {
      //       message: 'Account verified successfully',
      //       userId: existingUser.id,
      //     };
      //   }
      //   return {
      //     message: 'Account phone number verified successfully',
      //     userId: existingUser.id,
      //   };
      }
      else {
        // await this.otpService.update(otpEntry., { isUsed: true });

        return {
          message: 'OTP has been used successfully',
          userId: existingUser.id,
        };
      }
    } else {
      throw new BadRequestException(
        'OTP and user mismatched! Please try again.',
      );
      // return {
      //   message: '.',
      //   userId: existingUser.id,
      // };
    }
  }

  async sendPasswordResetEmail(email: string) {
    const user = await this.usersService.findUserByEmail(email);
    if (!user) {
      throw new BadRequestException('Email does not exist');
    }

    let frontendUrl = this.configService.getOrThrow<string>('FRONTEND_URL');
    const payload = { id: user.id };
    const jwtSecret = this.configService.getOrThrow('JWT_SECRET');

    const token = await this.jwtService.signAsync(payload, {
      secret: jwtSecret,
      expiresIn: '5m',
    });

    const resetLink = `${frontendUrl}/auth/reset-password?token=${token}`;

    // Get primary phone number
    const primaryPhone = await this.phoneNumberRepository.findOne({
      where: { user_id: user.id, is_primary: true }
    });

    console.log('===================');
    console.log(resetLink);
    console.log('===================');

    if (resetLink) {
      this.eventEmitter.emit('forgot-password.email', {
        resetLink,
        destination: NotificationDestination.EMAIL,
        id: user.id,
        email: user.email,
        phoneNumber: primaryPhone?.phone_number || '',
        firstName: user.first_name,
        userType: user.type,
      });
    }

    return { message: 'Password reset link has been sent to your email.' };
  }

  async sendPasswordResetPhone(phoneNumber: string) {
    const user = await this.usersService.findUserByPhone(phoneNumber);
    if (!user) {
      throw new BadRequestException('Phone number does not exist');
    }

    const createOtp = await this.otpService.create({
      useCase: OtpUseCase.PWDR,
      userId: user.id,
    });

    if (createOtp.code) {
      this.eventEmitter.emit('forgot-password.phone', {
        ...createOtp,
        destination: NotificationDestination.SMS,
        id: user.id,
        email: user.email,
        phoneNumber: phoneNumber,
        firstName: user.first_name,
        userType: user.type,
      });
    }

    return {
      message: 'Password reset otp has been sent to your phone number.',
    };
  }

  async resetPasswordEmail(
    token: string,
    resetPasswordDto: ResetPasswordEmailDto,
  ) {
    const { newPassword, confirmNewPassword } = resetPasswordDto;

    if (newPassword !== confirmNewPassword) {
      throw new BadRequestException('Passwords do not match.');
    }

    let payload;
    try {
      payload = this.jwtService.verify(token, {
        secret: this.configService.getOrThrow('JWT_SECRET'),
      });
    } catch (e) {
      throw new UnauthorizedException('Invalid or expired token.');
    }

    const user = await this.usersService.findById(payload.id);
    if (!user) {
      throw new BadRequestException('User not found.');
    }

    if (payload.id === user.id) {
      if (user.type === UserType.BUYER) {
        await this.usersService.updateBuyer(user.id, {
          password: newPassword,
        });
        return { message: 'Password has been reset successfully.' };
      }
      if (user.type === UserType.FARMER) {
        await this.usersService.updateFarmer(user.id, {
          password: newPassword,
        });
        return { message: 'Password has been reset successfully.' };
      }
    } else {
      throw new BadRequestException(
        'Password reset via email failed. Please try again',
      );
    }
  }

  async changePassword(
    userId: string,
    resetPasswordDto: ResetPasswordEmailDto,
  ) {
    const { newPassword, confirmNewPassword } = resetPasswordDto;

    if (newPassword !== confirmNewPassword) {
      throw new BadRequestException('Passwords do not match.');
    }

    const user = await this.usersService.findById(userId);
    if (!user) {
      throw new BadRequestException('User not found.');
    }

    if (user.type === UserType.BUYER) {
      await this.usersService.updateBuyer(user.id, {
        password: newPassword,
      });
      return { message: 'Password changed successfully' };
    }
    if (user.type === UserType.FARMER) {
      await this.usersService.updateFarmer(user.id, {
        password: newPassword,
      });
      return { message: 'Password changed successfully' };
    }
  }

  async resetPasswordPhone(resetPasswordDto: ResetPasswordDto) {
    const { otp, newPassword, confirmNewPassword } = resetPasswordDto;

    if (newPassword !== confirmNewPassword) {
      throw new BadRequestException('Passwords do not match.');
    }

    const verifiedOtp = await this.verifyOtp(otp);

    const existingUser = await this.usersService.findById(
      verifiedOtp.userId.toString(),
    );

    if (!existingUser) {
      throw new BadRequestException('User with the provided OTP is not found.');
    }

    if (verifiedOtp.message) {
      if (existingUser.type === UserType.BUYER) {
        await this.usersService.updateBuyer(existingUser.id, {
          password: newPassword,
        });
        return { message: 'Password has been reset successfully' };
      }
      if (existingUser.type === UserType.FARMER) {
        await this.usersService.updateFarmer(existingUser.id, {
          password: newPassword,
        });
        return { message: 'Password has been reset successfully' };
      }
    }
  }

  async login(loginDto: LoginDto) {
    const { email, password } = loginDto;

    const user = await this.usersService.findUserByEmail(email);
    if (!user) throw new NotFoundException('User not found');

    if (!user.verified) {
      // Get primary phone number
      const primaryPhone = await this.phoneNumberRepository.findOne({
        where: { user_id: user.id, is_primary: true }
      });

      return {
        message: 'Please verify your account first.',
        data: {
          email: user.email,
          phoneNumber: primaryPhone?.phone_number || ''
        },
      };
    }

    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      throw new ConflictException('Invalid password.');
    }

    // Get primary phone number for payload
    const primaryPhone = await this.phoneNumberRepository.findOne({
      where: { user_id: user.id, is_primary: true }
    });

    const payload = {
      id: user.id,
      firstName: user.first_name,
      lastName: user.last_name,
      email: user.email,
      phone: primaryPhone?.phone_number || '',
      userType: user.type,
      profilePicture: user.profile_picture
        ? user.profile_picture
        : 'https://t4.ftcdn.net/jpg/06/30/06/81/360_F_630068155_RnZI6mC91wz7gUYFVmhzwpl4O6x00Cbh.jpg',
    };

    const token = await this.signToken(payload);

    if (token) {
      if (user.type === UserType.BUYER) {
        await this.usersService.updateBuyer(user.id, {
          lastLogin: new Date(),
        });
      }
      if (user.type === UserType.FARMER) {
        await this.usersService.updateFarmer(user.id, {
          lastLogin: new Date(),
        });
      }
      return {
        message: 'Logged in successfully',
        userType: user.type,
        token,
      };
    }

    // Check user's OTP delivery preference
    // Send OTP via email or phone number
    // const sendOtp = await this.send2FACode(user.email);

    // console.log('-------------------------------');
    // console.log('Send OTP response', sendOtp);
    // console.log('-------------------------------');

    // if (sendOtp.message.includes('successfully)) {
    //   if (user.type === UserType.ADMIN) {
    //     // return { adminToken: user.adminToken };
    //   } else {
    //     return { message: sendOtp.message, userId: user.id };
    //   }
    //   return { message: sendOtp.message, userId: user.id };
    // } else {
    //   throw new BadRequestException('Failed to send OTP. Please try again.');
    // }
  }

  async verifyLogin(verifyLoginDto: VerifyLoginDto) {
    const user = await this.usersService.findById(verifyLoginDto.userId);
    if (!user) throw new NotFoundException('User not found');

    console.log('Verify login user', user);

    const otpVerified = await this.verifyOtp(verifyLoginDto.otp);

    if (otpVerified.userId === user.id) {
      // Get primary phone number
      const primaryPhone = await this.phoneNumberRepository.findOne({
        where: { user_id: user.id, is_primary: true }
      });

      const payload = {
        id: user.id,
        firstName: user.first_name,
        lastName: user.last_name,
        email: user.email,
        phone: primaryPhone?.phone_number || '',
        userType: user.type,
        profilePicture: user.profile_picture
          ? user.profile_picture
          : 'https://t4.ftcdn.net/jpg/06/30/06/81/360_F_630068155_RnZI6mC91wz7gUYFVmhzwpl4O6x00Cbh.jpg',
      };

      const token = await this.signToken(payload);

      if (token) {
        await this.userRepository.update(user.id, { last_login: new Date() });
        return {
          message: 'Logged in successfully',
          userType: user.type,
          token,
        };
      }
    }
  }

  async send2FACode(emailOrPhone: string) {
    const emailUser = await this.usersService.findUserByEmail(emailOrPhone);
    const phoneUser = await this.usersService.findUserByPhone(emailOrPhone);

    if (!emailUser && !phoneUser) {
      throw new NotFoundException(`User not found`);
    }

    let createOtp: any;
    let destination: any;
    let user: any;

    if (emailUser) {
      createOtp = await this.otpService.create({
        useCase: OtpUseCase['2FA'],
        userId: emailUser.id,
      });
      destination = NotificationDestination.EMAIL;
      user = emailUser;
    }

    if (phoneUser) {
      createOtp = await this.otpService.create({
        useCase: OtpUseCase['2FA'],
        userId: phoneUser.id,
      });
      destination = NotificationDestination.SMS;
      user = phoneUser;
    }

    // Get primary phone number
    const primaryPhone = await this.phoneNumberRepository.findOne({
      where: { user_id: user.id, is_primary: true }
    });

    this.eventEmitter.emit('verify.2fa', {
      ...createOtp,
      destination,
      id: user.id,
      email: user.email,
      phoneNumber: primaryPhone?.phone_number || emailOrPhone,
      userType: user.type,
      firstName: user.first_name,
    });

    return {
      message: '2FA code sent successfully',
      createOtp,
    };
  }

  // async verifyAdminLogin(
  //   verifyLoginDto: VerifyAdminLoginDto,
  //   @Res({ passthrough: true }) res: ExpressResponse,
  // ) {
  //   const token = await this.adminTokenModel.findOne({
  //     token: verifyLoginDto.adminToken,
  //   });

  //   const user = await this.usersService.findByEmail(token.adminEmail);
  //   if (!user) throw new NotFoundException('Admin not found');

  //   const otpVerified = await this.verifyOtp(verifyLoginDto.otp);

  //   if (otpVerified.message.includes('successfully)) {
  //     const payload = {
  //       id: user.id,
  //       name: user.firstName,
  //       email: user.email,
  //       phone: user.phoneNumber,
  //       userType: user.type,
  //     };

  //     const token = await this.signToken(payload);

  //     console.log(token);

  //     if (token) {
  //       // Set the JWT token as an HTTP-only cookie
  //       res.cookie('3w3k0AuthToken', token, {
  //         httpOnly: true,
  //         secure: process.env.NODE_ENV === 'production',
  //         maxAge: 3600000,
  //         sameSite: 'strict', // Protects against CSRF attacks
  //       });
  //       return { message: 'Logged in successfully' };
  //     }
  //   }

  private async signToken(args: { id: string; userType: UserType }) {
    const payload = args;
    const jwtSecret = this.configService.getOrThrow('JWT_SECRET');

    return await this.jwtService.signAsync(payload, { secret: jwtSecret });
  }
}
