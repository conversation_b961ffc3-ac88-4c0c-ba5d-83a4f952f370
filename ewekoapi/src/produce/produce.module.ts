import { Module } from '@nestjs/common';
import { ProduceService } from './produce.service';
import { ProduceController } from './produce.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { Produce, ProduceSchema } from './schema';
import { Category, CategorySchema } from 'src/category/schema';
import { PaginationService } from 'src/shared/pagination/pagination.service';
import { Address, AddressSchema } from 'src/addresses/schema';
import { AddressesService } from 'src/addresses/addresses.service';
import {
  AdminSchema,
  BuyerSchema,
  Farmer,
  FarmerSchema,
  FieldAgentSchema,
  LogisticsAgentSchema,
  User,
  UserSchema,
} from 'src/users/user.schema';
import { UserType } from 'src/shared/enums';

@Module({
  imports: [
    // Register User schema with discriminators
    MongooseModule.forFeatureAsync([
      {
        name: User.name,
        useFactory: () => {
          // Register discriminators using UserType enum values
          UserSchema.discriminator(UserType.BUYER, BuyerSchema);
          UserSchema.discriminator(UserType.FARMER, FarmerSchema);
          UserSchema.discriminator(UserType.AGENT, FieldAgentSchema);
          UserSchema.discriminator(
            UserType.LOGISTICS_AGENT,
            LogisticsAgentSchema,
          );
          UserSchema.discriminator(UserType.ADMIN, AdminSchema);

          return UserSchema;
        },
      },
    ]),
    MongooseModule.forFeature([
      { name: Farmer.name, schema: FarmerSchema },
      { name: Category.name, schema: CategorySchema },
      { name: Produce.name, schema: ProduceSchema },
    ]),
  ],
  controllers: [ProduceController],
  providers: [ProduceService, PaginationService],
})
export class ProduceModule {}
