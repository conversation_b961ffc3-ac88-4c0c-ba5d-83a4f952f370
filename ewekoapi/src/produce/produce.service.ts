import { CreateProduceDto } from './dto/create-produce.dto';
import { UpdateProduceDto } from './dto/update-produce.dto';

import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ConflictException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User, Farmer, Produce, Category } from '../users/entities/user.entity';
import { slugify } from 'src/shared/utils';
import { PaginationQueryDto } from 'src/shared/pagination/pagination-query.dto';
import { PaginationService } from 'src/shared/pagination/pagination.service';

@Injectable()
export class ProduceService {
  constructor(
    @InjectRepository(Produce)
    private produceRepository: Repository<Produce>,
    @InjectRepository(Category)
    private categoryRepository: Repository<Category>,
    @InjectRepository(Farmer)
    private farmerRepository: Repository<Farmer>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private readonly paginationService: PaginationService,
  ) {}

  async create(createProduceDto: CreateProduceDto) {
    console.log(createProduceDto);

    const { farmer, category, name } = createProduceDto;

    // Check if the farmer exists
    const existingFarmer = await this.farmerModel.findById(farmer);
    if (!existingFarmer) {
      throw new NotFoundException(`Farmer not found`);
    }

    // Check if the category exists
    const existingCategory = await this.categoryModel.findById(category);
    if (!existingCategory) {
      throw new NotFoundException(`Category not found`);
    }

    // Check for duplicate produce name
    const existingName = await this.productModel.findOne({ name });
    if (existingName) {
      throw new ConflictException(
        `A produce with the name "${name}" already exists`,
      );
    }

    // Generate and check slug
    const slug = slugify(name);
    const existingSlug = await this.productModel.findOne({ slug });
    if (existingSlug) {
      throw new ConflictException(
        `A produce with the slug "${slug}" already exists`,
      );
    }

    // Create and save the product
    const newProduct = new this.productModel({
      ...createProduceDto,
      farmer: existingFarmer,
      category: existingCategory,
      slug,
    });

    return newProduct.save();
  }

  // async findAll(paginationQuery: PaginationQueryDto) {
  //   const { page = 1, limit = 10 } = paginationQuery;

  //   // Get total count
  //   const total = await this.productModel.countDocuments();

  //   // Fetch paginated data
  //   const products = await this.productModel
  //     .find()
  //     .populate(['category', 'farmer'])
  //     .skip((page - 1) * limit)
  //     .limit(limit)
  //     .exec();

  //   // Return paginated response
  //   return this.paginationService.paginate(products, {
  //     page,
  //     limit,
  //   });
  // }

  // async findAll(paginationQuery: PaginationQueryDto, searchQuery?: string) {
  //   const { page = 1, limit = 10 } = paginationQuery;

  //   // Base query
  //   const baseQuery = searchQuery
  //     ? {
  //         $text: { $search: searchQuery },
  //       }
  //     : {};

  //   // Fetch paginated data with search
  //   const products = await this.productModel
  //     .find(baseQuery)
  //     .populate(['category', 'farmer'])
  //     .skip((page - 1) * limit)
  //     .limit(limit)
  //     .exec();

  //   // If using text search, sort by text score relevance
  //   if (searchQuery) {
  //     products.sort({ score: { $meta: 'textScore' } });
  //   }

  //   // Return paginated response
  //   return this.paginationService.paginate(products, {
  //     page,
  //     limit,
  //   });
  // }

  async findAll(paginationQuery: PaginationQueryDto, searchQuery?: string) {
    const { page = 1, limit = 10 } = paginationQuery;

    // Base query
    const baseQuery = searchQuery
      ? {
          name: { $regex: searchQuery, $options: 'i' },
        }
      : {};

    // Build the query
    let query = this.productModel.find(baseQuery);

    // Sort by name if using regex search (since we don't have text score)
    if (searchQuery) {
      query = query.sort({ name: 1 });
    }

    // Continue with pagination and population
    const products = await query
      .populate(['category', 'farmer'])
      .skip((page - 1) * limit)
      .limit(limit)
      .exec();

    // Return paginated response
    return this.paginationService.paginate(products, {
      page,
      limit,
    });
  }

  async findAllByFarmer(
    farmerId: string,
    paginationQuery: PaginationQueryDto,
    searchQuery?: string,
  ) {
    const { page = 1, limit = 10 } = paginationQuery;

    const filter: Record<string, any> = {
      farmer_id: farmerId,
    };

    if (searchQuery) {
      filter.name = { $regex: searchQuery, $options: 'i' };
    }

    const products = await this.productModel
      .find(filter)
      .populate(['category', 'farmer'])
      .skip((page - 1) * limit)
      .limit(limit)
      .exec();

    return this.paginationService.paginate(products, {
      page,
      limit,
    });
  }

  async findOne(id: string) {
    const product = await this.productModel
      .findById(id)
      .populate(['category', 'farmer'])
      .exec();
    if (!product) {
      throw new NotFoundException(`Produce not found`);
    }
    return product;
  }

  async update(
    id: string,
    updateProduceDto: UpdateProduceDto,
  ): Promise<Produce> {
    const updateProduceData = {
      ...updateProduceDto,
      slug: slugify(updateProduceDto.name),
    };
    const result = await this.produceRepository.update(id, updateProduceData);
    if (result.affected === 0) {
      throw new NotFoundException(`Produce with id ${id} not found`);
    }
    const updatedProduct = await this.produceRepository.findOne({ where: { id } })
      .exec();
    if (!updatedProduct) {
      throw new NotFoundException(`Produce not found`);
    }
    return updatedProduct;
  }

  async remove(id: string): Promise<{ message: string }> {
    const result = await this.produceRepository.delete(id);
    if (result.affected === 0) {
      throw new NotFoundException(`Produce not found`);
    }
    return { message: `Produce successfully deleted` };
  }
}
