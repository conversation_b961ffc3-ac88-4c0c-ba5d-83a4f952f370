import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Types, HydratedDocument } from 'mongoose';

export type ProduceDocument = HydratedDocument<Produce>;

@Schema({ timestamps: true })
export class Produce {
  @Prop({ type: Types.ObjectId, required: true, ref: 'Farmer' })
  farmer: Types.ObjectId;

  @Prop({ type: Types.ObjectId, required: true, ref: 'Category' })
  category: Types.ObjectId;

  @Prop({ type: String, required: true })
  name: string;

  @Prop({ type: String, required: true })
  description: string;

  @Prop({ type: Number, required: true })
  price: number;

  @Prop({ type: Number, required: true })
  negotiablePrice: number;

  @Prop({ type: Number, required: true })
  stock: number;

  @Prop({ type: Number, required: true })
  minOrderQty: number;

  @Prop({ type: Date, required: true })
  harvestDate: Date;

  @Prop({
    type: [String],
    required: true,
    validate: {
      validator: (images: string[]) =>
        images.every((url) =>
          /^https?:\/\/.+\.(jpg|jpeg|png|webp)$/i.test(url),
        ),
      message:
        'Each image must be a valid URL ending in .jpg, .jpeg, .png, or .webp',
    },
  })
  images: string[];

  @Prop({ type: String, required: true })
  slug: string; // Add slug field as unique
}

export const ProduceSchema = SchemaFactory.createForClass(Produce);

// Create compound indices for common query patterns
ProduceSchema.index({ farmer: 1, category: 1 }); // For filtering produces by farmer and category
ProduceSchema.index({ category: 1, price: 1 }); // For filtering and sorting produces by category and price
ProduceSchema.index({ harvestDate: -1, stock: -1 }); // For finding recent produces with available stock