import { Module } from '@nestjs/common';
import { AdminTokensService } from './admin-tokens.service';
import { AdminTokensController } from './admin-tokens.controller';
import { AdminToken, AdminTokenSchema } from './schema';
import { MongooseModule } from '@nestjs/mongoose';
import { UsersService } from 'src/users/users.service';

import { Address, AddressSchema } from 'src/addresses/schema';
import { Order, OrderSchema } from 'src/orders/schema';
import { Produce, ProduceSchema } from 'src/produce/schema';
import { Wallet, WalletSchema } from 'src/wallets/schema';
import { WalletsService } from 'src/wallets/wallets.service';
import {
  Admin,
  AdminSchema,
  Business,
  BusinessSchema,
  Buyer,
  BuyerSchema,
  Contact,
  ContactSchema,
  Farmer,
  FarmerSchema,
  FieldAgent,
  FieldAgentSchema,
  LogisticsAgentSchema,
  Profile,
  ProfileSchema,
  User,
  UserSchema,
} from 'src/users/user.schema';
import { UserType } from 'src/shared/enums';
import { CartService } from 'src/cart/cart.service';
import { PreferencesService } from 'src/preferences/preferences.service';
import { Cart, CartItem, CartItemSchema, CartSchema } from 'src/cart/schema';
import { Preferences, PreferencesSchema } from 'src/preferences/schema';
import { Notification, NotificationSchema } from 'src/notifications/schemas';

@Module({
  imports: [
    // Register User schema with discriminators
    MongooseModule.forFeatureAsync([
      {
        name: User.name,
        useFactory: () => {
          // Register discriminators using UserType enum values
          UserSchema.discriminator(UserType.BUYER, BuyerSchema);
          UserSchema.discriminator(UserType.FARMER, FarmerSchema);
          UserSchema.discriminator(UserType.AGENT, FieldAgentSchema);
          UserSchema.discriminator(
            UserType.LOGISTICS_AGENT,
            LogisticsAgentSchema,
          );
          UserSchema.discriminator(UserType.ADMIN, AdminSchema);

          return UserSchema;
        },
      },
    ]),
    MongooseModule.forFeature([
      { name: AdminToken.name, schema: AdminTokenSchema },
      { name: Buyer.name, schema: BuyerSchema },
      { name: Farmer.name, schema: FarmerSchema },
      { name: FieldAgent.name, schema: FieldAgentSchema },
      { name: Admin.name, schema: AdminSchema },
      { name: Address.name, schema: AddressSchema },
      { name: Order.name, schema: OrderSchema },
      { name: Produce.name, schema: ProduceSchema },
      { name: Wallet.name, schema: WalletSchema },
      { name: Profile.name, schema: ProfileSchema },
      { name: Contact.name, schema: ContactSchema },
      { name: Business.name, schema: BusinessSchema },
      { name: Cart.name, schema: CartSchema },
      { name: Preferences.name, schema: PreferencesSchema },
      { name: CartItem.name, schema: CartItemSchema },
      { name: Notification.name, schema: NotificationSchema },
    ]),
  ],
  controllers: [AdminTokensController],
  providers: [
    AdminTokensService,
    UsersService,
    WalletsService,
    CartService,
    PreferencesService,
  ],
})
export class AdminTokensModule {}
