import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';

export type AdminTokenDocument = HydratedDocument<AdminToken>;

@Schema({ timestamps: true })
export class AdminToken {
  @Prop({ type: String, required: true, unique: true })
  token: string;

  @Prop({ type: String, required: true })
  adminEmail: string;

  @Prop({
    type: Date,
    required: true,
  })
  expiresAt: Date;

  @Prop({
    type: Boolean,
    default: false,
  })
  isSuper?: boolean;

  @Prop({
    type: Boolean,
    default: true,
  })
  isActive: boolean;
}

export const AdminTokenSchema = SchemaFactory.createForClass(AdminToken);

// Indexing for automatic expiration (TTL index)
AdminTokenSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });
