import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ConflictException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Model, Types } from 'mongoose';
import {
  CreateAdminTokenDto,
  RenewAdminTokenDto,
} from './dto/create-admin-token.dto';
import { AdminToken } from '../users/entities/user.entity';
import { UsersService } from 'src/users/users.service';
import { add, addMinutes, isBefore, isEqual } from 'date-fns';

@Injectable()
export class AdminTokensService {
  constructor(
    @InjectRepository(AdminToken)
    private adminTokenRepository: Repository<AdminToken>,
    private userService: UsersService,
  ) {}

  // async create(createAdminTokenDto: CreateAdminTokenDto): Promise<AdminToken> {
  //   const { adminEmail, isSuper } = createAdminTokenDto;

  //   await this.adminTokenModel.updateMany(
  //     { adminEmail },
  //     { $set: { isActive: false } },
  //   );

  //   const token = await this.generateAdminToken(adminEmail, isSuper);

  //   const createdAdminToken = new this.adminTokenModel({
  //     token,
  //     adminEmail,
  //     isSuper,
  //     expiresAt: this.expiryDate(),
  //   });

  //   return createdAdminToken.save();
  // }

  // async renew(renewAdminTokenDto: RenewAdminTokenDto) {
  //   const { adminToken } = renewAdminTokenDto;

  //   const verifiedToken = await this.adminTokenModel.findOne({
  //     token: adminToken,
  //   });

  //   if (!verifiedToken) {
  //     throw new NotFoundException(
  //       `AdminToken with token ${adminToken} not found`,
  //     );
  //   }

  //   const { adminEmail, isSuper } = verifiedToken;

  //   const verifiedAdmin = await this.userService.findByEmail(adminEmail);

  //   if (!verifiedAdmin || verifiedAdmin.userType !== 'ADMIN') {
  //     throw new ConflictException(
  //       `Admin with the token ${verifiedToken.token} not found`,
  //     );
  //   }

  //   console.log(verifiedToken);
  //   console.log(verifiedAdmin);

  //   await this.adminTokenModel.deleteOne({ token: adminToken });

  //   const token = await this.generateAdminToken(adminEmail, isSuper);

  //   const createdAdminToken = new this.adminTokenModel({
  //     token,
  //     adminEmail,
  //     isSuper,
  //     expiresAt: this.expiryDate(),
  //   });

  //   console.log(createdAdminToken);

  //   const savedToken = createdAdminToken.save();

  //   // Update the adminToken field in the admin document

  //   return savedToken;
  // }

  // async findAll(): Promise<AdminToken[]> {
  //   return this.adminTokenModel.find().exec();
  // }

  // async findOne(id: string): Promise<AdminToken> {
  //   const adminToken = await this.adminTokenModel.findById(new Types.ObjectId(id)).exec();
  //   if (!adminToken) {
  //     throw new NotFoundException(`AdminToken with ID ${id} not found`);
  //   }
  //   return adminToken;
  // }

  // async deactivateToken(token: string): Promise<AdminToken> {
  //   const adminToken = await this.adminTokenModel.findOne({ token }).exec();

  //   if (!adminToken) {
  //     throw new NotFoundException(`AdminToken with token ${token} not found`);
  //   }

  //   adminToken.isActive = false;

  //   return adminToken.save();
  // }

  // async deleteToken(token: string): Promise<void> {
  //   const result = await this.adminTokenModel.deleteOne({ token }).exec();

  //   if (result.deletedCount === 0) {
  //     throw new NotFoundException(`AdminToken with token ${token} not found`);
  //   }
  // }

  // private async generateAdminToken(
  //   email: string,
  //   isSuper: boolean,
  // ): Promise<string> {
  //   const characters = 'EWEKO0123456789' + email.toUpperCase();
  //   let token = '3WK';

  //   if (isSuper) {
  //     token += '$PR';
  //     for (let i = 0; i < 10; i++) {
  //       const randomIndex = Math.floor(Math.random() * characters.length);
  //       token += characters[randomIndex];
  //     }
  //   } else {
  //     for (let i = 0; i < 13; i++) {
  //       const randomIndex = Math.floor(Math.random() * characters.length);
  //       token += characters[randomIndex];
  //     }
  //   }

  //   return token;
  // }

  // private expiryDate = (): Date => {
  //   const createdAt = new Date();
  //   return add(createdAt, { months: 3 });
  // };

  // private isTokenExpired(expiry: Date): boolean {
  //   const currentDate = new Date();
  //   return isBefore(expiry, currentDate) || isEqual(expiry, currentDate);
  // }
}
