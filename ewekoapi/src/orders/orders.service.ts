import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CreateOrderDto, OrderResponseDto } from './dto/create-order.dto';
import { UpdateOrderDto } from './dto/update-order.dto';
import { User, Order, Produce } from '../users/entities/user.entity';
import { UsersService } from 'src/users/users.service';
import { PaginationQueryDto } from 'src/shared/pagination/pagination-query.dto';
import { PaginationService } from 'src/shared/pagination/pagination.service';
import { OrderStatus, PaymentStatus } from 'src/shared/enums';

@Injectable()
export class OrdersService {
  constructor(
    @InjectRepository(Order)
    private readonly orderRepository: Repository<Order>,
    @InjectRepository(Produce)
    private readonly produceRepository: Repository<Produce>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly userService: UsersService,
    private readonly paginationService: PaginationService,
  ) {}

  // Create a new order
  async create(createOrderDto: CreateOrderDto) {
    // Verify if the user exists
    const user = await this.userService.findById(createOrderDto.userId);
    if (!user) {
      throw new NotFoundException(`User not found`);
    }

    let orderItems = [];

    // Verify if all items in the order exist and are available
    for (const item of createOrderDto.items) {
      const produce = await this.produceModel
        .findById(new Types.ObjectId(item.produce))
        .exec();
      if (!produce) {
        throw new NotFoundException(`Produce not found`);
      }

      // Optional: Add more checks, like verifying if enough stock is available
      if (produce.stock < item.quantity) {
        throw new BadRequestException(
          `Not enough stock for produce with id ${item.produce}`,
        );
      }
      const orderItem = await this.orderItemModel.create({
        produce,
        quantity: item.quantity,
        totalPrice: item.totalPrice,
      });
      const savedOrderItem = await orderItem.save();
      orderItems.push(savedOrderItem);
    }

    // Calculate final total cost including shipping fee
    const finalTotalCost =
      createOrderDto.totalCost + createOrderDto.shippingFee;

    // Create the order document
    const createdOrder = new this.orderModel({
      ...createOrderDto,
      finalTotalCost,
      items: orderItems,
    });

    // Save the order to the database
    await createdOrder.save();

    // Return the created order
    return createdOrder.toObject();
  }

  // Find all orders
  async findAll(paginationQuery: PaginationQueryDto) {
    const { page = 1, limit = 10 } = paginationQuery;

    // Get total count
    const total = await this.orderModel.countDocuments();

    // Fetch paginated data
    const orders = await this.orderModel
      .find()
      .populate('items.produce')
      .skip((page - 1) * limit)
      .limit(limit)
      .exec();

    // Return paginated response
    return this.paginationService.paginate(orders, {
      page,
      limit,
    });
  }

  // Find all orders for a specific buyer
  async findAllByBuyer(userId: string, paginationQuery: PaginationQueryDto) {
    const { page = 1, limit = 10 } = paginationQuery;

    // Fetch paginated data for this buyer
    const orders = await this.orderModel
      .find({ userId })
      .populate('items.produce')
      .skip((page - 1) * limit)
      .limit(limit)
      .exec();

    // Return paginated response
    return this.paginationService.paginate(orders, {
      page,
      limit,
    });
  }

  // Find a specific order by ID
  async findOne(id: string) {
    const order = await this.orderModel
      .findById(new Types.ObjectId(id))
      .populate('items.produce')
      .exec();
    if (!order) {
      throw new NotFoundException(`Order not found`);
    }
    return order.toObject();
  }

  // Update an existing order
  async update(id: string, updateOrderDto: UpdateOrderDto) {
    const order = await this.orderModel.findById(new Types.ObjectId(id)).exec();
    if (!order) {
      throw new NotFoundException(`Order not found`);
    }

    // Update the order details
    Object.assign(order, updateOrderDto);

    // Save the updated order
    await order.save();

    // Return the updated order
    return order.toObject();
  }

  async deleteAllOrders(): Promise<{ message: string; deletedCount: number }> {
    const result = await this.orderModel.deleteMany({});
    return {
      message: 'All orders have been deleted',
      deletedCount: result.deletedCount,
    };
  }
}
