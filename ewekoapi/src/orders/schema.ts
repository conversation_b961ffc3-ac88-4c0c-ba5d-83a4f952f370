import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';
import { OrderStatus, PaymentStatus } from 'src/shared/enums';
import { Produce } from 'src/produce/schema';

@Schema({ timestamps: true })
export class OrderItem {
  @Prop({ type: Types.ObjectId, ref: 'Produce', required: true })
  produce: Produce;

  @Prop({ type: Number, required: true, min: 1 })
  quantity: number;

  @Prop({ type: Number, required: true })
  totalPrice: number;
}

export const OrderItemSchema = SchemaFactory.createForClass(OrderItem);

@Schema({ timestamps: true })
export class Order {
  @Prop({ type: Types.ObjectId, required: true })
  userId: Types.ObjectId;

  @Prop({ type: [OrderItemSchema], default: [] })
  items: OrderItem[];

  @Prop({ type: Number, required: true })
  totalCost: number;

  @Prop({ type: Number, required: true })
  shippingFee: number; // New field for shipping fee

  @Prop({
    type: String,
    enum: OrderStatus,
    default: OrderStatus.PROCESSING,
  })
  status: string;

  @Prop({ type: String, required: true })
  paymentMethod: string;

  @Prop({
    type: String,
    enum: PaymentStatus,
    default: PaymentStatus.PENDING,
  })
  paymentStatus: string;

  @Prop({ type: String, required: true })
  shippingAddress: string;

  @Prop({ type: Date, default: Date.now }) // Default to current date
  paymentDate: Date;

  @Prop({ type: Date, default: null }) // Nullable field
  shippedDate?: Date | null;

  @Prop({ type: Number, required: true })
  finalTotalCost: number; // Final total cost including shipping fee
}

export const OrderSchema = SchemaFactory.createForClass(Order);

export type OrderItemDocument = HydratedDocument<OrderItem>;
export type OrderDocument = HydratedDocument<Order>;