import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsBoolean,
  IsEnum,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';

// Create DTO
export class CreatePreferencesDto {
  @ApiProperty({
    description: 'User ID reference',
    type: String,
    required: true,
    example: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
  })
  @IsUUID()
  user: string;
}

// Update DTO
enum NotificationChannel {
  EMAIL = 'EMAIL',
  SMS = 'SMS',
  BOTH = 'BOTH',
}

export class UpdatePreferencesDto {
  @ApiPropertyOptional({
    description: 'Preferred destination for OTP codes',
    enum: NotificationChannel,
    example: 'BOTH',
  })
  @IsEnum(NotificationChannel)
  @IsOptional()
  otpDestination?: NotificationChannel;

  @ApiPropertyOptional({
    description: 'Receive promotional emails or messages',
    example: false,
  })
  @IsBoolean()
  @IsOptional()
  receivePromotions?: boolean;

  @ApiPropertyOptional({
    description: 'Enable or disable 2FA',
    example: false,
  })
  @IsBoolean()
  @IsOptional()
  enable2fa?: boolean;

  @ApiPropertyOptional({
    description: 'Enable or disable general update notifications',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  generalUpdates?: boolean;

  @ApiPropertyOptional({
    description: 'Enable or disable order update notifications',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  orderUpdates?: boolean;

  @ApiPropertyOptional({
    description: 'Enable or disable transaction update notifications',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  transactionUpdates?: boolean;

  @ApiPropertyOptional({
    description: 'Enable or disable payment update notifications',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  paymentUpdates?: boolean;

  @ApiPropertyOptional({
    description: 'Enable or disable delivery update notifications',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  deliveryUpdates?: boolean;
}

export class PreferencesResponseDto {
  @ApiProperty({
    description: 'Unique identifier of the preferences record',
    type: String,
    example: '60d21b4667d0d8992e610c85',
  })
  _id: string;

  @ApiProperty({
    description: 'User ID reference',
    type: String,
    example: '60d21b4667d0d8992e610c85',
  })
  user: Types.ObjectId;

  @ApiProperty({
    description: 'Preferred destination for OTP codes',
    enum: ['EMAIL', 'SMS', 'BOTH'],
    example: 'EMAIL',
  })
  otpDestination: 'EMAIL' | 'SMS' | 'BOTH';

  @ApiProperty({
    description: 'Whether to receive promotional communications',
    type: Boolean,
    example: true,
  })
  receivePromotions: boolean;

  @ApiProperty({
    description: 'Whether to enable or disable 2FA',
    type: Boolean,
    example: true,
  })
  enable2fa: boolean;

  @ApiProperty({
    description: 'General updates notification preferences',
    type: NotificationChannelsDto,
    example: { enabled: true, channels: ['EMAIL'] },
  })
  generalUpdates: NotificationChannelsDto;

  @ApiProperty({
    description: 'Order updates notification preferences',
    type: NotificationChannelsDto,
    example: { enabled: true, channels: ['EMAIL', 'SMS'] },
  })
  orderUpdates: NotificationChannelsDto;

  @ApiProperty({
    description: 'Transaction updates notification preferences',
    type: NotificationChannelsDto,
    example: { enabled: false, channels: ['EMAIL'] },
  })
  transactionUpdates: NotificationChannelsDto;

  @ApiProperty({
    description: 'Payment updates notification preferences',
    type: NotificationChannelsDto,
    example: { enabled: true, channels: ['SMS'] },
  })
  paymentUpdates: NotificationChannelsDto;

  @ApiProperty({
    description: 'Delivery updates notification preferences',
    type: NotificationChannelsDto,
    example: { enabled: true, channels: ['EMAIL', 'SMS'] },
  })
  deliveryUpdates: NotificationChannelsDto;

  @ApiProperty({
    description: 'Creation timestamp',
    type: Date,
    example: '2023-03-01T12:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Last update timestamp',
    type: Date,
    example: '2023-03-02T14:30:00.000Z',
  })
  updatedAt: Date;
}
