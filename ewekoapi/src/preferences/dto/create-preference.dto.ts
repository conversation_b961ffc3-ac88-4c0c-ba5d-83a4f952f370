import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsBoolean,
  IsEnum,
  IsOptional,
  IsString,
  IsUUID,
  IsObject,
} from 'class-validator';

// Create DTO
export class CreatePreferencesDto {
  @ApiProperty({
    description: 'User ID reference',
    type: String,
    required: true,
    example: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
  })
  @IsUUID()
  user: string;
}

// Update DTO
export class UpdatePreferencesDto {
  @ApiPropertyOptional({
    description: 'Enable or disable email notifications',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  email_notifications?: boolean;

  @ApiPropertyOptional({
    description: 'Enable or disable SMS notifications',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  sms_notifications?: boolean;

  @ApiPropertyOptional({
    description: 'Enable or disable push notifications',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  push_notifications?: boolean;

  @ApiPropertyOptional({
    description: 'User preferred language',
    example: 'en',
  })
  @IsString()
  @IsOptional()
  language?: string;

  @ApiPropertyOptional({
    description: 'User preferred theme',
    example: 'light',
  })
  @IsString()
  @IsOptional()
  theme?: string;

  @ApiPropertyOptional({
    description: 'Additional notification preferences as JSON',
    example: { order_updates: true, payment_updates: false },
  })
  @IsObject()
  @IsOptional()
  notification_preferences?: any;
}

export class PreferencesResponseDto {
  @ApiProperty({
    description: 'Unique identifier of the preferences record',
    type: String,
    example: '60d21b4667d0d8992e610c85',
  })
  _id: string;

  @ApiProperty({
    description: 'User ID reference',
    type: String,
    example: '60d21b4667d0d8992e610c85',
  })
  user: Types.ObjectId;

  @ApiProperty({
    description: 'Preferred destination for OTP codes',
    enum: ['EMAIL', 'SMS', 'BOTH'],
    example: 'EMAIL',
  })
  otpDestination: 'EMAIL' | 'SMS' | 'BOTH';

  @ApiProperty({
    description: 'Whether to receive promotional communications',
    type: Boolean,
    example: true,
  })
  receivePromotions: boolean;

  @ApiProperty({
    description: 'Whether to enable or disable 2FA',
    type: Boolean,
    example: true,
  })
  enable2fa: boolean;

  @ApiProperty({
    description: 'General updates notification preferences',
    type: NotificationChannelsDto,
    example: { enabled: true, channels: ['EMAIL'] },
  })
  generalUpdates: NotificationChannelsDto;

  @ApiProperty({
    description: 'Order updates notification preferences',
    type: NotificationChannelsDto,
    example: { enabled: true, channels: ['EMAIL', 'SMS'] },
  })
  orderUpdates: NotificationChannelsDto;

  @ApiProperty({
    description: 'Transaction updates notification preferences',
    type: NotificationChannelsDto,
    example: { enabled: false, channels: ['EMAIL'] },
  })
  transactionUpdates: NotificationChannelsDto;

  @ApiProperty({
    description: 'Payment updates notification preferences',
    type: NotificationChannelsDto,
    example: { enabled: true, channels: ['SMS'] },
  })
  paymentUpdates: NotificationChannelsDto;

  @ApiProperty({
    description: 'Delivery updates notification preferences',
    type: NotificationChannelsDto,
    example: { enabled: true, channels: ['EMAIL', 'SMS'] },
  })
  deliveryUpdates: NotificationChannelsDto;

  @ApiProperty({
    description: 'Creation timestamp',
    type: Date,
    example: '2023-03-01T12:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Last update timestamp',
    type: Date,
    example: '2023-03-02T14:30:00.000Z',
  })
  updatedAt: Date;
}
