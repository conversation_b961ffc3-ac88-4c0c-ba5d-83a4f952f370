import { Modu<PERSON> } from '@nestjs/common';
import { PreferencesService } from './preferences.service';
import { PreferencesController } from './preferences.controller';
import { MongooseModule } from '@nestjs/mongoose';

import { Address, AddressSchema } from 'src/addresses/schema';
import { Notification, NotificationSchema } from 'src/notifications/schemas';
import { Preferences, PreferencesSchema } from './schema';
import { Admin, AdminSchema, Buyer, BuyerSchema, Farmer, FarmerSchema, FieldAgent, FieldAgentSchema, LogisticsAgentSchema, User, UserSchema } from 'src/users/user.schema';
import { UserType } from 'src/shared/enums';

@Module({
  imports: [
    // Register User schema with discriminators
    MongooseModule.forFeatureAsync([
      {
        name: User.name,
        useFactory: () => {
          // Register discriminators using UserType enum values
          UserSchema.discriminator(UserType.BUYER, BuyerSchema);
          UserSchema.discriminator(UserType.FARMER, FarmerSchema);
          UserSchema.discriminator(UserType.AGENT, FieldAgentSchema);
          UserSchema.discriminator(
            UserType.LOGISTICS_AGENT,
            LogisticsAgentSchema,
          );
          UserSchema.discriminator(UserType.ADMIN, AdminSchema);

          return UserSchema;
        },
      },
    ]),
    MongooseModule.forFeature([
      { name: Preferences.name, schema: PreferencesSchema },
      { name: Notification.name, schema: NotificationSchema },
      { name: Buyer.name, schema: BuyerSchema },
      { name: Farmer.name, schema: FarmerSchema },
      { name: FieldAgent.name, schema: FieldAgentSchema },
      { name: Admin.name, schema: AdminSchema },
      { name: Address.name, schema: AddressSchema },
    ]),
  ],
  controllers: [PreferencesController],
  providers: [PreferencesService],
})
export class PreferencesModule {}
