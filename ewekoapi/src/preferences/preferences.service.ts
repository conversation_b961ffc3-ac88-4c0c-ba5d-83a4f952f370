import {
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import {
  CreatePreferencesDto,
  UpdatePreferencesDto,
} from './dto/create-preference.dto';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';

import { Notification, NotificationDocument } from 'src/notifications/schemas';
import { Preferences, PreferencesDocument } from './schema';
import {
  Buyer,
  BuyerDocument,
  Farmer,
  FarmerDocument,
} from 'src/users/user.schema';

@Injectable()
export class PreferencesService {
  constructor(
    @InjectModel(Preferences.name)
    private readonly preferencesModel: Model<PreferencesDocument>,
    @InjectModel(Buyer.name) private buyerModel: Model<BuyerDocument>,
    @InjectModel(Farmer.name) private farmerModel: Model<FarmerDocument>,
    @InjectModel(Notification.name)
    private notificationModel: Model<NotificationDocument>,
  ) {}

  async create(
    createPreferencesDto: CreatePreferencesDto,
  ): Promise<Preferences> {
    const userId = new Types.ObjectId(createPreferencesDto.user);

    // Check if preferences already exist for this user
    const existingPreferences = await this.preferencesModel.findOne({
      user: createPreferencesDto.user,
    });
    if (existingPreferences) {
      throw new ConflictException('Preferences already exist for this user.');
    }

    // Create new preferences with default values
    const newPreferences = new this.preferencesModel({ user: userId });
    return newPreferences.save();
  }

  async findAll(): Promise<Preferences[]> {
    return this.preferencesModel.find().exec();
  }

  async findOne(userId: string): Promise<Preferences> {
    if (!Types.ObjectId.isValid(userId)) {
      throw new NotFoundException('Invalid preference ID format');
    }

    const preference = await this.preferencesModel
      .findOne({ user: new Types.ObjectId(userId) })
      .exec();

    if (!preference) {
      throw new NotFoundException('Preferences not found');
    }

    return preference;
  }

  async update(
    userId: string,
    updatePreferencesDto: UpdatePreferencesDto,
  ): Promise<Preferences> {
    const preferences = await this.preferencesModel.findOne({
      user: new Types.ObjectId(userId),
    });

    if (!preferences) {
      throw new NotFoundException(`Preferences for this user not found.`);
    }

    // Update only the provided fields
    Object.assign(preferences, updatePreferencesDto);

    return preferences.save();
  }

  async remove(userId: string): Promise<void> {
    const deletedPreferences = await this.preferencesModel.findOneAndDelete({
      user: new Types.ObjectId(userId),
    });

    if (!deletedPreferences) {
      throw new NotFoundException(`Preferences not found for user ${userId}`);
    }
  }
}
