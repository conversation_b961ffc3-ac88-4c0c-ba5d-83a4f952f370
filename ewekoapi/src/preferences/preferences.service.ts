import {
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import {
  CreatePreferencesDto,
  UpdatePreferencesDto,
} from './dto/create-preference.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User, Preferences } from '../users/entities/user.entity';

@Injectable()
export class PreferencesService {
  constructor(
    @InjectRepository(Preferences)
    private readonly preferencesRepository: Repository<Preferences>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  async create(
    createPreferencesDto: CreatePreferencesDto,
  ): Promise<Preferences> {
    // Check if preferences already exist for this user
    const existingPreferences = await this.preferencesRepository.findOne({
      where: { user_id: createPreferencesDto.user },
    });
    if (existingPreferences) {
      throw new ConflictException('Preferences already exist for this user.');
    }

    // Create new preferences with default values
    const newPreferences = this.preferencesRepository.create({
      user_id: createPreferencesDto.user
    });
    return this.preferencesRepository.save(newPreferences);
  }

  async findAll(): Promise<Preferences[]> {
    return this.preferencesRepository.find();
  }

  async findOne(userId: string): Promise<Preferences> {
    const preference = await this.preferencesRepository.findOne({
      where: { user_id: userId },
    });

    if (!preference) {
      throw new NotFoundException('Preferences not found');
    }

    return preference;
  }

  async update(
    userId: string,
    updatePreferencesDto: UpdatePreferencesDto,
  ): Promise<Preferences> {
    const result = await this.preferencesRepository.update(
      { user_id: userId },
      updatePreferencesDto
    );

    if (result.affected === 0) {
      throw new NotFoundException(`Preferences for this user not found.`);
    }

    return this.findOne(userId);
  }

  async remove(userId: string): Promise<void> {
    const result = await this.preferencesRepository.delete({ user_id: userId });

    if (result.affected === 0) {
      throw new NotFoundException(`Preferences not found for user ${userId}`);
    }
  }
}
