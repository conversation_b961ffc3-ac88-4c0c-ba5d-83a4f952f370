import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';

export type PreferencesDocument = HydratedDocument<Preferences>;

@Schema({ timestamps: true })
export class Preferences {
  @Prop({ type: Types.ObjectId, required: true, unique: true })
  user: Types.ObjectId;

  @Prop({ type: String, enum: ['EMAIL', 'SMS', 'BOTH'], default: 'EMAIL' })
  otpDestination: 'EMAIL' | 'SMS' | 'BOTH';

  @Prop({ type: Boolean, default: true })
  receivePromotions: boolean;

  @Prop({ type: Boolean, default: false })
  enable2fa: boolean;

  @Prop({ type: Boolean, default: true })
  generalUpdates: boolean;

  @Prop({ type: Boolean, default: true })
  orderUpdates: boolean;

  @Prop({ type: Boolean, default: true })
  transactionUpdates: boolean;

  @Prop({ type: Boolean, default: true })
  paymentUpdates: boolean;

  @Prop({ type: Boolean, default: true })
  deliveryUpdates: boolean;
}

export const PreferencesSchema = SchemaFactory.createForClass(Preferences);
