import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';
import { OtpUseCase } from 'src/shared/enums';

export type OtpDocument = HydratedDocument<Otp>;

@Schema({ timestamps: true })
export class Otp {
  @Prop({ required: true })
  code: string;

  @Prop({
    type: String,
    enum: Object.values(OtpUseCase),
    required: true,
  })
  useCase: OtpUseCase;

  @Prop({ required: true })
  expiresAt: Date;

  @Prop({ default: false })
  isUsed: boolean;

  @Prop({ required: true })
  userId: Types.ObjectId;
}

export const OtpSchema = SchemaFactory.createForClass(Otp);
