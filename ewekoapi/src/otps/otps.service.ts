import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateOtpDto } from './dto/create-otp.dto';
import { UpdateOtpDto } from './dto/update-otp.dto';
import { InjectModel } from '@nestjs/mongoose';
import { Otp } from './schema';
import { Model, Types } from 'mongoose';
import { addMinutes, isBefore, isEqual } from 'date-fns';
import { Type } from 'class-transformer';

@Injectable()
export class OtpsService {
  constructor(@InjectModel(Otp.name) private readonly otpModel: Model<Otp>) {}

  async create(createOtpDto: CreateOtpDto) {
    // const otp = await new this.otpModel({
    //   ...createOtpDto,
    //   code: this.generateOTP(),
    //   expiresAt: this.getExpiry(),
    // });

    const otp = await this.otpModel.create({
      ...createOtpDto,
      code: this.generateOTP(),
      expiresAt: this.getExpiry(),
    });

    console.error(otp);

    const savedOtp = await otp.save();

    return {
      code: savedOtp.code,
      useCase: savedOtp.useCase,
    };
  }

  async findAll() {
    return await this.otpModel.find();
  }

  async findById(id: string): Promise<Otp> {
    const otp = await this.otpModel.findById(id);
    if (!otp) {
      throw new NotFoundException(`OTP not found`);
    }
    return otp;
  }

  async findByCode(code: string): Promise<Otp> {
    const otp = await this.otpModel.findOne({ code }).exec();

    if (!otp) {
      throw new NotFoundException(`OTP not found`);
    }
    return otp;
  }

  async update(id: string, updateOtpDto: UpdateOtpDto): Promise<Otp> {
    const objectId = new Types.ObjectId(id);
    const otp = await this.otpModel.findByIdAndUpdate(objectId, updateOtpDto, {
      new: true,
    });

    if (!otp) {
      throw new NotFoundException(`OTP not found`);
    }
    return otp;
  }

  async remove(id: string): Promise<void> {
    const otp = await this.otpModel.findByIdAndDelete(new Types.ObjectId(id));

    if (!otp) {
      throw new NotFoundException(`OTP not found`);
    }
  }

  private generateOTP = (): string => {
    const digits = '0123456789';
    let otp = '';

    for (let i = 0; i < 6; i++) {
      otp += digits[Math.floor(Math.random() * digits.length)];
    }

    return otp;
  };

  private getExpiry = (): Date => {
    const createdAt = new Date();
    return addMinutes(createdAt, 5);
  };

  private isTokenExpired(expiry: Date): boolean {
    const currentDate = new Date();
    return isBefore(expiry, currentDate) || isEqual(expiry, currentDate);
  }
}
