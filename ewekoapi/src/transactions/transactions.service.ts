import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateTransactionDto } from './dto/create-transaction.dto';
import { UpdateTransactionDto } from './dto/update-transaction.dto';
import { ConfigService } from '@nestjs/config';
import { randomBytes } from 'crypto';
import { InjectModel } from '@nestjs/mongoose';
import { Transaction, TransactionDocument } from './schema';
import { Model, Types } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';
import { PaymentStatus } from 'src/shared/enums';
import { OrdersService } from 'src/orders/orders.service';
import { Order, OrderDocument } from 'src/orders/schema';
import { UsersService } from 'src/users/users.service';
import { PaginationQueryDto } from 'src/shared/pagination/pagination-query.dto';
import { PaginationService } from 'src/shared/pagination/pagination.service';
import { Produce, ProduceDocument } from 'src/produce/schema';
import {
  Buyer,
  BuyerDocument,
  Farmer,
  FarmerDocument,
} from 'src/users/user.schema';

@Injectable()
export class TransactionsService {
  constructor(
    private readonly configService: ConfigService,
    private readonly userService: UsersService,
    @InjectModel(Transaction.name)
    private readonly transactionModel: Model<TransactionDocument>,
    @InjectModel(Order.name)
    private readonly orderModel: Model<OrderDocument>,
    @InjectModel(Produce.name)
    private readonly produceModel: Model<ProduceDocument>,
    @InjectModel(Buyer.name)
    private readonly buyerModel: Model<BuyerDocument>,
    @InjectModel(Farmer.name)
    private readonly farmerModel: Model<FarmerDocument>,
    private readonly paginationService: PaginationService,
  ) {}

  async create(createTransactionDto: CreateTransactionDto) {
    const { user, order, invoice } = createTransactionDto;

    const existingUser = await this.userService.findBuyerById(user);
    if (!existingUser) throw new NotFoundException('User not found');

    const existingOrder = await this.orderModel.findById(
      new Types.ObjectId(order),
    );
    if (!existingOrder) throw new NotFoundException('Order not found');

    // if (invoice) {
    //   const existingInvoice = await this.invoiceModel.findById(invoice);
    //   if (!existingInvoice) throw new NotFoundException('Invoice not found');
    // }

    const transaction = new this.transactionModel({
      ...createTransactionDto,
      reference: this.generateTransactionReference(),
      status: PaymentStatus.PENDING,
    });

    const savedTrx = transaction.save();

    existingUser.transactions.push((await savedTrx).id);
    await existingUser.save();

    return savedTrx;
  }

  async findAll(paginationQuery: PaginationQueryDto) {
    const { page = 1, limit = 10 } = paginationQuery;

    // Get total count
    const total = await this.transactionModel.countDocuments();

    // Fetch paginated data
    const transactions = await this.transactionModel
      .find()
      .skip((page - 1) * limit)
      .limit(limit)
      .exec();

    // Return paginated response
    return this.paginationService.paginate(transactions, {
      page,
      limit,
    });
  }

  async findAllByFarmer(farmerId: string, paginationQuery: PaginationQueryDto) {
    const { page = 1, limit = 10 } = paginationQuery;

    try {
      // Convert string farmerId to ObjectId
      const farmerObjectId = new Types.ObjectId(farmerId);

      // Find all produce items from this farmer
      const farmerProduce = await this.produceModel
        .find({ farmer: farmerObjectId })
        .select('_id')
        .lean()
        .exec();

      if (!farmerProduce || farmerProduce.length === 0) {
        // If no produce found, return empty paginated response
        return this.paginationService.paginate([], {
          page,
          limit,
        });
      }

      // Extract produce IDs as ObjectIds
      const produceIds = farmerProduce.map((produce) => produce._id);

      // Find orders containing the farmer's produce
      const ordersWithFarmerProduce = await this.orderModel
        .find({
          'items.produce': { $in: produceIds },
        })
        .select('_id')
        .lean()
        .exec();

      if (!ordersWithFarmerProduce || ordersWithFarmerProduce.length === 0) {
        // If no orders found, return empty paginated response
        return this.paginationService.paginate([], {
          page,
          limit,
        });
      }

      // Extract just the order IDs from the results
      const orderIds = ordersWithFarmerProduce.map((order) =>
        order._id.toString(),
      );

      // Fetch paginated transactions for these order IDs
      const farmerTransactions = await this.transactionModel
        .find({ order: { $in: orderIds } })
        .skip((page - 1) * limit)
        .limit(limit)
        .sort({ createdAt: -1 })
        .lean()
        .exec();

      // Calculate farmer's portion of each transaction
      const enhancedTransactions = farmerTransactions.map((transaction) => {
        const order = transaction.order as any;

        return {
          ...transaction,
        };
      });

      // Return paginated response
      return this.paginationService.paginate(enhancedTransactions, {
        page,
        limit,
      });
    } catch (error) {
      console.error('Error finding farmer transactions:', error);
      throw error;
    }
  }

  async findAllByBuyer(
    buyerId: string,
    paginationQuery: PaginationQueryDto,
  ): Promise<any> {
    const { page = 1, limit = 10 } = paginationQuery;

    try {
      // Fetch paginated transactions for these order IDs
      const buyerTransactions = await this.transactionModel
        .find({ user: buyerId })
        .skip((page - 1) * limit)
        .limit(limit)
        .sort({ createdAt: -1 })
        .lean()
        .exec();

      // Calculate farmer's portion of each transaction
      const enhancedTransactions = buyerTransactions.map((transaction) => {
        return {
          ...transaction,
        };
      });

      // Return paginated response
      return this.paginationService.paginate(enhancedTransactions, {
        page,
        limit,
      });
    } catch (error) {
      console.error('Error finding farmer transactions:', error);
      throw error;
    }
  }

  async findOne(id: string): Promise<Transaction> {
    const transaction = await this.transactionModel
      .findById(new Types.ObjectId(id))
      .exec();
    if (!transaction) {
      throw new NotFoundException(`Transaction not found`);
    }
    return transaction;
  }

  async update(
    id: string,
    updateTransactionDto: UpdateTransactionDto,
  ): Promise<Transaction> {
    const transaction = await this.transactionModel
      .findByIdAndUpdate(new Types.ObjectId(id), updateTransactionDto, {
        new: true,
      })
      .exec();

    if (!transaction) {
      throw new NotFoundException(`Transaction not found`);
    }

    return transaction;
  }

  async deleteAllTransactions(): Promise<{
    message: string;
    deletedCount: number;
  }> {
    const result = await this.transactionModel.deleteMany({});
    return {
      message: 'All transactions have been deleted',
      deletedCount: result.deletedCount,
    };
  }

  private generateTransactionReference(): string {
    return `EWKTRX-${uuidv4().replace(/-/g, '').slice(0, 12).toUpperCase()}`;
  }
}
