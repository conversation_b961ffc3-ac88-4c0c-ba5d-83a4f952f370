import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Types, Document, HydratedDocument } from 'mongoose';
import { PaymentMethod, PaymentStatus, PaymentType } from 'src/shared/enums';

export type TransactionDocument = HydratedDocument<Transaction>;

@Schema({ timestamps: true })
export class Transaction {
  @Prop({ type: Types.ObjectId, required: true })
  user: Types.ObjectId;

  @Prop({ type: Types.ObjectId, required: true, ref: 'Order' })
  order: Types.ObjectId;

  @Prop({ type: Types.ObjectId, required: false, ref: 'Invoice' })
  invoice?: Types.ObjectId;

  @Prop({ type: Number, required: true })
  totalAmount: number;

  @Prop({ type: String, enum: PaymentStatus, required: true })
  status: PaymentStatus;

  @Prop({ type: String, enum: PaymentMethod, required: true })
  paymentMethod: PaymentMethod;

  @Prop({ type: String, enum: PaymentType, required: true })
  paymentType: PaymentType;

  @Prop({ required: true })
  reference: string;

  @Prop({ type: Boolean, default: false })
  processed: boolean;
}

export const TransactionSchema = SchemaFactory.createForClass(Transaction);
 