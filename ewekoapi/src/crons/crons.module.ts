import { <PERSON>du<PERSON> } from '@nestjs/common';
import { CronsService } from './crons.service';
import { MongooseModule } from '@nestjs/mongoose';
import { Otp, OtpSchema } from 'src/otps/schema';
import { Notification, NotificationSchema } from 'src/notifications/schemas';
import {
  Buyer,
  BuyerSchema,
  Farmer,
  FarmerSchema,
} from 'src/users/user.schema';
import { WeeklyPriceModule } from '../weekly-price/weekly-price.module';

@Module({
  imports: [
    WeeklyPriceModule,
    MongooseModule.forFeature([
      { name: Otp.name, schema: OtpSchema },
      { name: Notification.name, schema: NotificationSchema },
      { name: Buyer.name, schema: BuyerSchema },
      { name: Farmer.name, schema: FarmerSchema },
    ]),
  ],
  controllers: [],
  providers: [CronsService],
})
export class CronsModule {}
