import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Otp } from 'src/otps/schema';
import { Notification, NotificationDocument } from 'src/notifications/schemas';
import { Cron, CronExpression } from '@nestjs/schedule';
import { isBefore, subDays } from 'date-fns';
import {
  Buyer,
  BuyerDocument,
  Farmer,
  FarmerDocument,
} from 'src/users/user.schema';
import { UserType } from 'src/shared/enums';
import { WeeklyPriceService } from '../weekly-price/weekly-price.service';

@Injectable()
export class CronsService {
  private readonly logger = new Logger(CronsService.name);

  constructor(
    @InjectModel(Otp.name)
    private readonly otpModel: Model<Otp>,
    @InjectModel(Notification.name)
    private readonly notificationModel: Model<NotificationDocument>,
    @InjectModel(Buyer.name)
    private readonly buyerModel: Model<BuyerDocument>,
    @InjectModel(Farmer.name)
    private readonly farmerModel: Model<FarmerDocument>,
    private readonly weeklyPriceService: WeeklyPriceService,
  ) {}

  // Weekly price calculation job - runs every Sunday at midnight currently set to run every 30 minutes
  @Cron(CronExpression.EVERY_30_MINUTES)
  async handleWeeklyPriceCalculation() {
    console.log('------------------------');
    console.log('Starting weekly price calculation job');
    
    try {
      await this.weeklyPriceService.calculateWeeklyAverages();
      console.log('Successfully completed weekly price calculation');
      console.log('------------------------');
    } catch (error) {
      console.error('Error in weekly price calculation:', error);
      console.log('------------------------');
      throw error;
    }
  }

  // Cron job to delete expired OTPs
  @Cron(CronExpression.EVERY_5_MINUTES)
  async deleteExpiredOtps() {
    const currentDate = new Date();

    const expiredOtps = await this.otpModel.find().exec();
    const otpsToDelete = expiredOtps.filter((otp) =>
      isBefore(new Date(otp.expiresAt), currentDate),
    );

    if (otpsToDelete.length > 0) {
      await this.otpModel.deleteMany({
        _id: { $in: otpsToDelete.map((otp) => otp._id) },
      });
      console.log('------------------------');
      console.log(`Deleted ${otpsToDelete.length} expired OTPs`);
      console.log('------------------------');
    }
  }

  // Cron job to delete notifications older than 14 days
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async deleteOldNotifications() {
    const thirtyDaysAgo = subDays(new Date(), 14);

    const oldNotifications = await this.notificationModel.find({
      created_at: { $lt: thirtyDaysAgo },
    });

    // Delete old notifications from the Notification collection
    const result = await this.notificationModel.deleteMany({
      created_at: { $lt: thirtyDaysAgo },
    });

    if (result.deletedCount > 0) {
      console.log('--------------------------');
      console.log(`Deleted ${result.deletedCount} old notifications`);
      console.log('--------------------------');

      // Remove the notification ID from the users' notifications arrays
      for (const notification of oldNotifications) {
        const { userId } = notification;
        let user;

        // Check if user is a Buyer or Farmer and fetch the user
        if (notification.userType === UserType.BUYER) {
          user = await this.buyerModel.findById(userId);
        } else if (notification.userType === UserType.FARMER) {
          user = await this.farmerModel.findById(userId);
        }

        if (user) {
          user.notifications = user.notifications.filter(
            (notificationId) =>
              notificationId.toString() !== notification._id.toString(),
          );
          await user.save();
        }
      }
    }
  }

  @Cron(CronExpression.EVERY_5_MINUTES)
  async deleteReadNotifications() {
    try {
      // Find and delete read notifications
      const readNotifications = await this.notificationModel.find({
        isRead: true,
      });

      const result = await this.notificationModel
        .deleteMany({
          isRead: true,
        })
        .exec();

      if (result.deletedCount > 0) {
        console.log('------------------------');
        console.log(`Deleted ${result.deletedCount} read notifications`);
        console.log('------------------------');

        // Remove the notification ID from the users' notifications arrays
        for (const notification of readNotifications) {
          const { userId } = notification;
          let user;

          // Check if user is a Buyer or Farmer and fetch the user
          if (notification.userType === UserType.BUYER) {
            user = await this.buyerModel.findById(userId);
          } else if (notification.userType === UserType.FARMER) {
            user = await this.farmerModel.findById(userId);
          }

          if (user) {
            user.notifications = user.notifications.filter(
              (notificationId) =>
                notificationId.toString() !== notification._id.toString(),
            );
            await user.save();
          }
        }
      }
    } catch (error) {
      console.error('Error deleting read notifications:', error);
    }
  }
}
