import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import {
  User,
  Farmer,
  Buyer,
  Admin,
  Address,
  PhoneNumber,
  Order,
  Produce,
  Wallet
} from './entities/user.entity';
import { UserType } from 'src/shared/enums';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Farmer)
    private readonly farmerRepository: Repository<Farmer>,
    @InjectRepository(Buyer)
    private readonly buyerRepository: Repository<Buyer>,
    @InjectRepository(Admin)
    private readonly adminRepository: Repository<Admin>,
    @InjectRepository(Address)
    private readonly addressRepository: Repository<Address>,
    @InjectRepository(PhoneNumber)
    private readonly phoneRepository: Repository<PhoneNumber>,
    @InjectRepository(Order)
    private readonly orderRepository: Repository<Order>,
    @InjectRepository(Produce)
    private readonly produceRepository: Repository<Produce>,
    @InjectRepository(Wallet)
    private readonly walletRepository: Repository<Wallet>,
  ) {}

  async create(createUserDto: CreateUserDto): Promise<User> {
    const user = this.userRepository.create(createUserDto);
    return await this.userRepository.save(user);
  }

  async findAll(): Promise<User[]> {
    return await this.userRepository.find({
      relations: ['addresses', 'phone_numbers']
    });
  }

  async findOne(id: string): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { id },
      relations: ['addresses', 'phone_numbers']
    });
    if (!user) {
      throw new NotFoundException(`User with id ${id} not found`);
    }
    return user;
  }

  async findById(id: string): Promise<User> {
    return this.findOne(id);
  }

  async update(id: string, updateUserDto: UpdateUserDto): Promise<User> {
    const result = await this.userRepository.update(id, updateUserDto);
    if (result.affected === 0) {
      throw new NotFoundException(`User with id ${id} not found`);
    }
    return this.findOne(id);
  }

  async remove(id: string): Promise<void> {
    const result = await this.userRepository.delete(id);
    if (result.affected === 0) {
      throw new NotFoundException(`User with id ${id} not found`);
    }
  }

  async findUserByEmail(email: string): Promise<User | null> {
    return await this.userRepository.findOne({
      where: { email },
      relations: ['addresses', 'phone_numbers']
    });
  }

  async findUserByPhone(phone: string): Promise<User | null> {
    const phoneRecord = await this.phoneRepository.findOne({
      where: { phone_number: phone },
      relations: ['user']
    });
    return phoneRecord?.user || null;
  }

  async findUserByUsername(username: string): Promise<User | null> {
    return await this.userRepository.findOne({
      where: { username },
      relations: ['addresses', 'phone_numbers']
    });
  }

  async createBuyer(signUpDto: any): Promise<User> {
    // Create user first
    const user = this.userRepository.create({
      first_name: signUpDto.firstName,
      last_name: signUpDto.lastName,
      email: signUpDto.email,
      username: signUpDto.username,
      password: signUpDto.password,
      type: UserType.BUYER,
      is_email_verified: false,
      is_phone_verified: false,
    });
    const savedUser = await this.userRepository.save(user);

    // Create buyer profile
    const buyer = this.buyerRepository.create({
      user_id: savedUser.id,
    });
    await this.buyerRepository.save(buyer);

    // Create phone number
    if (signUpDto.primaryPhone) {
      const phone = this.phoneRepository.create({
        user_id: savedUser.id,
        phone_number: signUpDto.primaryPhone,
        is_primary: true,
      });
      await this.phoneRepository.save(phone);
    }

    return savedUser;
  }

  async createFarmer(signUpDto: any): Promise<User> {
    // Create user first
    const user = this.userRepository.create({
      first_name: signUpDto.firstName,
      last_name: signUpDto.lastName,
      email: signUpDto.email,
      username: signUpDto.username,
      password: signUpDto.password,
      type: UserType.FARMER,
      is_email_verified: false,
      is_phone_verified: false,
    });
    const savedUser = await this.userRepository.save(user);

    // Create farmer profile
    const farmer = this.farmerRepository.create({
      user_id: savedUser.id,
    });
    await this.farmerRepository.save(farmer);

    // Create phone number
    if (signUpDto.primaryPhone) {
      const phone = this.phoneRepository.create({
        user_id: savedUser.id,
        phone_number: signUpDto.primaryPhone,
        is_primary: true,
      });
      await this.phoneRepository.save(phone);
    }

    return savedUser;
  }

  async getFarmerDashboard(farmerId: string) {
    const farmer = await this.farmerRepository.findOne({
      where: { id: farmerId },
      relations: ['user']
    });

    if (!farmer) {
      throw new NotFoundException('Farmer not found');
    }

    // Get farmer's produce count
    const produceCount = await this.produceRepository.count({
      where: { farmer_id: farmerId }
    });

    // Get farmer's wallet
    const wallet = await this.walletRepository.findOne({
      where: { farmer_id: farmerId }
    });

    return {
      farmer,
      stats: {
        produceCount,
        ordersCount: 0, // Orders are not directly linked to farmers
        balance: wallet?.balance || 0,
        grossRevenue: wallet?.gross_revenue || 0,
      }
    };
  }
}
