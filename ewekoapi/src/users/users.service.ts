import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CreateUserDto, CreateBuyerDto, CreateFarmerDto, CreatePhoneNumberDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import {
  User,
  Farmer,
  Buyer,
  Admin,
  PhoneNumber
} from './entities/user.entity';
import { UserType, AdminRole } from '../shared/enums';
import * as bcrypt from 'bcryptjs';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(PhoneNumber)
    private phoneRepository: Repository<PhoneNumber>,
    @InjectRepository(Farmer)
    private farmerRepository: Repository<Farmer>,
    @InjectRepository(Buyer)
    private buyerRepository: Repository<Buyer>,
    @InjectRepository(Admin)
    private adminRepository: Repository<Admin>,
  ) {}

  async create(createUserDto: CreateUserDto) {
    const { type, ...userData } = createUserDto;

    // Hash password
    if (userData.password) {
      userData.password = await bcrypt.hash(userData.password, 10);
    }

    // Create user with all fields in one table
    const user = this.userRepository.create({
      ...userData,
      type,
    });
    const savedUser = await this.userRepository.save(user);

    // Create type-specific profile
    if (type === UserType.FARMER) {
      const farmer = this.farmerRepository.create({
        user_id: savedUser.id,
      });
      await this.farmerRepository.save(farmer);
    } else if (type === UserType.BUYER) {
      const buyer = this.buyerRepository.create({
        user_id: savedUser.id,
        loyalty_points: 0,
      });
      await this.buyerRepository.save(buyer);
    } else if (type === UserType.ADMIN) {
      const admin = this.adminRepository.create({
        user_id: savedUser.id,
        role: AdminRole.SUB_ADMIN,
      });
      await this.adminRepository.save(admin);
    }

    return this.findOne(savedUser.id);
  }

  async findAll() {
    return this.userRepository.find({
      relations: ['farmer_profile', 'buyer_profile', 'admin_profile'],
    });
  }

  async findOne(id: string) {
    return this.userRepository.findOne({
      where: { id },
      relations: ['farmer_profile', 'buyer_profile', 'admin_profile'],
    });
  }

  async findByUsername(username: string) {
    return this.userRepository.findOne({
      where: { username },
      relations: ['farmer_profile', 'buyer_profile', 'admin_profile'],
    });
  }

  async findByEmail(email: string) {
    return this.userRepository.findOne({
      where: { email },
      relations: ['farmer_profile', 'buyer_profile', 'admin_profile'],
    });
  }

  async findByPhone(phone: string) {
    // Find user by phone number in the phone_numbers table
    const phoneRecord = await this.phoneRepository.findOne({
      where: { phone_number: phone },
      relations: ['user'],
    });

    if (!phoneRecord) return null;

    return this.userRepository.findOne({
      where: { id: phoneRecord.user_id },
      relations: ['farmer_profile', 'buyer_profile', 'admin_profile'],
    });
  }

  async update(id: string, updateUserDto: UpdateUserDto) {
    // Hash password if provided
    if (updateUserDto.password) {
      updateUserDto.password = await bcrypt.hash(updateUserDto.password, 10);
    }

    await this.userRepository.update(id, updateUserDto);
    return this.findOne(id);
  }

  async remove(id: string) {
    return this.userRepository.delete(id);
  }

  // Backward compatibility methods
  async findById(id: string) {
    return this.findOne(id);
  }

  async findUserByEmail(email: string) {
    return this.findByEmail(email);
  }

  async findUserByPhone(phone: string) {
    return this.findByPhone(phone);
  }

  async findUserByUsername(username: string) {
    return this.findByUsername(username);
  }

  async createBuyer(createBuyerDto: CreateBuyerDto) {
    // Map frontend fields to database fields
    const userData = {
      ...createBuyerDto,
      first_name: createBuyerDto.firstName || createBuyerDto.first_name,
      last_name: createBuyerDto.lastName || createBuyerDto.last_name,
      username: createBuyerDto.username || createBuyerDto.primaryPhone,
      type: UserType.BUYER,
    };

    // Remove frontend-specific fields
    delete userData.firstName;
    delete userData.lastName;
    delete userData.primaryPhone;

    const user = await this.create(userData);

    // Add primary phone number if provided
    if (createBuyerDto.primaryPhone) {
      await this.addPhoneNumber(user.id, {
        phone_number: createBuyerDto.primaryPhone,
        type: 'personal',
        is_primary: true,
      });
    }

    return user;
  }

  async createFarmer(createFarmerDto: CreateFarmerDto) {
    // Map frontend fields to database fields
    const userData = {
      ...createFarmerDto,
      first_name: createFarmerDto.firstName || createFarmerDto.first_name,
      last_name: createFarmerDto.lastName || createFarmerDto.last_name,
      business_name: createFarmerDto.farmName || createFarmerDto.business_name,
      username: createFarmerDto.username || createFarmerDto.primaryPhone,
      type: UserType.FARMER,
    };

    // Remove frontend-specific fields
    delete userData.firstName;
    delete userData.lastName;
    delete userData.farmName;
    delete userData.primaryPhone;

    const user = await this.create(userData);

    // Add primary phone number if provided
    if (createFarmerDto.primaryPhone) {
      await this.addPhoneNumber(user.id, {
        phone_number: createFarmerDto.primaryPhone,
        type: 'personal',
        is_primary: true,
      });
    }

    return user;
  }

  async addPhoneNumber(userId: string, phoneDto: CreatePhoneNumberDto) {
    // If this is being set as primary, unset other primary phones
    if (phoneDto.is_primary) {
      await this.phoneRepository.update(
        { user_id: userId, is_primary: true },
        { is_primary: false }
      );
    }

    const phone = this.phoneRepository.create({
      user_id: userId,
      ...phoneDto,
    });

    return this.phoneRepository.save(phone);
  }

  async updateBuyer(id: string, updateData: any) {
    return this.update(id, updateData);
  }

  async updateFarmer(id: string, updateData: any) {
    return this.update(id, updateData);
  }
}
