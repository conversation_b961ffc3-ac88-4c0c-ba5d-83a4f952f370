import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import {
  User,
  Farmer,
  Buyer,
  Admin
} from './entities/user.entity';
import { UserType, AdminRole } from '../shared/enums';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(Farmer)
    private farmerRepository: Repository<Farmer>,
    @InjectRepository(Buyer)
    private buyerRepository: Repository<Buyer>,
    @InjectRepository(Admin)
    private adminRepository: Repository<Admin>,
  ) {}

  async create(createUserDto: CreateUserDto) {
    const { type, ...userData } = createUserDto;

    // Create user with all fields in one table
    const user = this.userRepository.create({
      ...userData,
      type,
    });
    const savedUser = await this.userRepository.save(user);

    // Create type-specific profile
    if (type === UserType.FARMER) {
      const farmer = this.farmerRepository.create({
        user_id: savedUser.id,
      });
      await this.farmerRepository.save(farmer);
    } else if (type === UserType.BUYER) {
      const buyer = this.buyerRepository.create({
        user_id: savedUser.id,
        loyalty_points: 0,
      });
      await this.buyerRepository.save(buyer);
    } else if (type === UserType.ADMIN) {
      const admin = this.adminRepository.create({
        user_id: savedUser.id,
        role: AdminRole.SUB_ADMIN,
      });
      await this.adminRepository.save(admin);
    }

    return this.findOne(savedUser.id);
  }

  async findAll() {
    return this.userRepository.find({
      relations: ['farmer_profile', 'buyer_profile', 'admin_profile'],
    });
  }

  async findOne(id: string) {
    return this.userRepository.findOne({
      where: { id },
      relations: ['farmer_profile', 'buyer_profile', 'admin_profile'],
    });
  }

  async findByUsername(username: string) {
    return this.userRepository.findOne({
      where: { username },
      relations: ['farmer_profile', 'buyer_profile', 'admin_profile'],
    });
  }

  async findByEmail(email: string) {
    return this.userRepository.findOne({
      where: { email },
      relations: ['farmer_profile', 'buyer_profile', 'admin_profile'],
    });
  }

  async findByPhone(phone: string) {
    return this.userRepository.findOne({
      where: { phone },
      relations: ['farmer_profile', 'buyer_profile', 'admin_profile'],
    });
  }

  async update(id: string, updateUserDto: UpdateUserDto) {
    await this.userRepository.update(id, updateUserDto);
    return this.findOne(id);
  }

  async remove(id: string) {
    return this.userRepository.delete(id);
  }

  // Backward compatibility methods
  async findById(id: string) {
    return this.findOne(id);
  }

  async findUserByEmail(email: string) {
    return this.findByEmail(email);
  }

  async findUserByPhone(phone: string) {
    return this.findByPhone(phone);
  }

  async findUserByUsername(username: string) {
    return this.findByUsername(username);
  }

  async createBuyer(createBuyerDto: any) {
    return this.create({ ...createBuyerDto, type: UserType.BUYER });
  }

  async createFarmer(createFarmerDto: any) {
    return this.create({ ...createFarmerDto, type: UserType.FARMER });
  }

  async updateBuyer(id: string, updateData: any) {
    return this.update(id, updateData);
  }

  async updateFarmer(id: string, updateData: any) {
    return this.update(id, updateData);
  }

  async findAllBuyers() {
    return this.userRepository.find({
      where: { type: UserType.BUYER },
      relations: ['buyer_profile'],
    });
  }

  async findAllFarmers() {
    return this.userRepository.find({
      where: { type: UserType.FARMER },
      relations: ['farmer_profile'],
    });
  }

  async findBuyerById(id: string) {
    return this.userRepository.findOne({
      where: { id, type: UserType.BUYER },
      relations: ['buyer_profile'],
    });
  }

  async findFarmerById(id: string) {
    return this.userRepository.findOne({
      where: { id, type: UserType.FARMER },
      relations: ['farmer_profile'],
    });
  }

  async deleteBuyer(id: string) {
    return this.remove(id);
  }

  async deleteFarmer(id: string) {
    return this.remove(id);
  }

  async findAllUsers(options: { skip?: number; limit?: number } = {}) {
    return this.userRepository.find({
      skip: options.skip || 0,
      take: options.limit || 10,
      relations: ['farmer_profile', 'buyer_profile', 'admin_profile'],
    });
  }

  async searchUsers(query: string, options: { skip?: number; limit?: number } = {}) {
    return this.userRepository.find({
      where: [
        { username: query },
        { email: query },
        { first_name: query },
        { last_name: query },
      ],
      skip: options.skip || 0,
      take: options.limit || 10,
      relations: ['farmer_profile', 'buyer_profile', 'admin_profile'],
    });
  }

  async getUserStats() {
    const totalUsers = await this.userRepository.count();
    const totalBuyers = await this.userRepository.count({ where: { type: UserType.BUYER } });
    const totalFarmers = await this.userRepository.count({ where: { type: UserType.FARMER } });
    const totalAdmins = await this.userRepository.count({ where: { type: UserType.ADMIN } });

    return {
      totalUsers,
      totalBuyers,
      totalFarmers,
      totalAdmins,
    };
  }

  async getFarmerDashboard(farmerId: string) {
    const farmer = await this.findFarmerById(farmerId);
    return farmer;
  }
}
