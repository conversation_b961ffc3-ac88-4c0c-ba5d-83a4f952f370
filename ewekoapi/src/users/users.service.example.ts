import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CreateUserDto, CreatePhoneNumberDto, CreateAddressDto } from './dto/create-user.dto';
import { 
  User, 
  Farmer, 
  Buyer, 
  Admin,
  PhoneNumber,
  Address
} from './entities/user.entity';
import { UserType, AdminRole } from '../shared/enums';

@Injectable()
export class UsersServiceExample {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(PhoneNumber)
    private phoneRepository: Repository<PhoneNumber>,
    @InjectRepository(Address)
    private addressRepository: Repository<Address>,
    @InjectRepository(Farmer)
    private farmerRepository: Repository<Farmer>,
    @InjectRepository(Buyer)
    private buyerRepository: Repository<Buyer>,
    @InjectRepository(Admin)
    private adminRepository: Repository<Admin>,
  ) {}

  async createUserWithMultipleContacts(
    createUserDto: CreateUserDto,
    additionalPhones?: CreatePhoneNumberDto[],
    addresses?: CreateAddressDto[]
  ) {
    // 1. Create user with primary contact info
    const user = this.userRepository.create({
      ...createUserDto,
    });
    const savedUser = await this.userRepository.save(user);

    // 2. Add primary phone as a phone number record
    const primaryPhone = this.phoneRepository.create({
      user_id: savedUser.id,
      phone_number: createUserDto.primary_phone,
      type: 'personal',
      is_primary: true,
      is_verified: false,
    });
    await this.phoneRepository.save(primaryPhone);

    // 3. Add additional phone numbers if provided
    if (additionalPhones?.length) {
      const phoneNumbers = additionalPhones.map(phone => 
        this.phoneRepository.create({
          user_id: savedUser.id,
          ...phone,
        })
      );
      await this.phoneRepository.save(phoneNumbers);
    }

    // 4. Add addresses if provided
    if (addresses?.length) {
      const userAddresses = addresses.map(address => 
        this.addressRepository.create({
          user_id: savedUser.id,
          ...address,
        })
      );
      const savedAddresses = await this.addressRepository.save(userAddresses);
      
      // Set primary address if one is marked as primary
      const primaryAddress = savedAddresses.find(addr => addr.is_primary);
      if (primaryAddress) {
        await this.userRepository.update(savedUser.id, {
          primary_address_id: primaryAddress.id,
        });
      }
    }

    // 5. Create type-specific profile
    if (createUserDto.type === UserType.FARMER) {
      const farmer = this.farmerRepository.create({
        user_id: savedUser.id,
      });
      await this.farmerRepository.save(farmer);
    } else if (createUserDto.type === UserType.BUYER) {
      const buyer = this.buyerRepository.create({
        user_id: savedUser.id,
        loyalty_points: 0,
      });
      await this.buyerRepository.save(buyer);
    } else if (createUserDto.type === UserType.ADMIN) {
      const admin = this.adminRepository.create({
        user_id: savedUser.id,
        role: AdminRole.SUB_ADMIN,
      });
      await this.adminRepository.save(admin);
    }

    return this.findUserWithAllDetails(savedUser.id);
  }

  async findUserWithAllDetails(id: string) {
    return this.userRepository.findOne({
      where: { id },
      relations: [
        'phone_numbers',
        'addresses', 
        'primary_address',
        'farmer_profile', 
        'buyer_profile', 
        'admin_profile'
      ],
    });
  }

  async addPhoneNumber(userId: string, phoneDto: CreatePhoneNumberDto) {
    // If this is being set as primary, unset other primary phones
    if (phoneDto.is_primary) {
      await this.phoneRepository.update(
        { user_id: userId, is_primary: true },
        { is_primary: false }
      );
      
      // Update user's primary phone
      await this.userRepository.update(userId, {
        primary_phone: phoneDto.phone_number,
      });
    }

    const phone = this.phoneRepository.create({
      user_id: userId,
      ...phoneDto,
    });
    
    return this.phoneRepository.save(phone);
  }

  async addAddress(userId: string, addressDto: CreateAddressDto) {
    // If this is being set as primary, unset other primary addresses
    if (addressDto.is_primary) {
      await this.addressRepository.update(
        { user_id: userId, is_primary: true },
        { is_primary: false }
      );
    }

    const address = this.addressRepository.create({
      user_id: userId,
      ...addressDto,
    });
    
    const savedAddress = await this.addressRepository.save(address);

    // Update user's primary address if this is primary
    if (addressDto.is_primary) {
      await this.userRepository.update(userId, {
        primary_address_id: savedAddress.id,
      });
    }

    return savedAddress;
  }

  async getUserPhones(userId: string) {
    return this.phoneRepository.find({
      where: { user_id: userId },
      order: { is_primary: 'DESC', created_at: 'ASC' },
    });
  }

  async getUserAddresses(userId: string) {
    return this.addressRepository.find({
      where: { user_id: userId },
      order: { is_primary: 'DESC', created_at: 'ASC' },
    });
  }
}
