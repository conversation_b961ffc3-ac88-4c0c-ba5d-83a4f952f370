// user.schema.ts
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, HydratedDocument, Types } from 'mongoose';
import { AdminRole, UserType } from 'src/shared/enums';

@Schema({ timestamps: true, discriminatorKey: 'userType' })
export class User {
  @Prop({ required: true })
  username: string;

  @Prop({ required: true })
  password: string;

  @Prop({ default: false })
  verified: boolean;

  @Prop({ default: true })
  isActive: boolean;

  @Prop({ default: null })
  lastLogin: Date | null;

  @Prop({ type: Types.ObjectId, ref: 'Profile' })
  profile: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'Contact' })
  contact: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'Business' })
  business: Types.ObjectId;

  // @Prop({ type: [Types.ObjectId], ref: 'Address' })
  // addresses: Types.ObjectId;

  @Prop({ type: [Types.ObjectId], ref: 'Address', default: [] })
  addresses: Types.ObjectId[];

  @Prop({ type: [Types.ObjectId], ref: 'Notification' })
  notifications?: Types.ObjectId[];

  @Prop({ type: String, enum: Object.values(UserType), required: true })
  type: UserType;
}

export type UserDocument = HydratedDocument<User>;
export const UserSchema = SchemaFactory.createForClass(User);

// 2. Profile Schema - Personal information
@Schema()
export class Profile {
  @Prop({ required: true })
  firstName: string;

  @Prop({ required: true })
  lastName: string;

  @Prop()
  middleName?: string;

  @Prop()
  prefix?: string;

  @Prop({ enum: ['male', 'female', 'other'], required: false })
  gender?: string;

  @Prop()
  dateOfBirth?: string;

  @Prop()
  profilePicture?: string;
}

export type ProfileDocument = HydratedDocument<Profile>;
export const ProfileSchema = SchemaFactory.createForClass(Profile);

// 3. Contact Schema - Communication information
@Schema()
export class Contact {
  @Prop({ required: true })
  primaryPhone: string;

  @Prop()
  secondaryPhone?: string;

  @Prop({ required: true })
  email: string;
}

export type ContactDocument = HydratedDocument<Contact>;
export const ContactSchema = SchemaFactory.createForClass(Contact);
// Explicit compound index (optional for future expansion)
ContactSchema.index({ primaryPhone: 1 });
ContactSchema.index({ email: 1 });

// 4. Address Schema - Location information
@Schema()
export class Address {
  @Prop()
  street: string;

  @Prop()
  community: string;

  @Prop()
  lga: string;

  @Prop()
  city: string;

  @Prop()
  state: string;
}

export type AddressDocument = HydratedDocument<Address>;
export const AddressSchema = SchemaFactory.createForClass(Address);

// 5. Business Schema - Business-related details
@Schema()
export class Business {
  @Prop()
  businessName: string;
}

export type BusinessDocument = HydratedDocument<Business>;
export const BusinessSchema = SchemaFactory.createForClass(Business);

// Buyer Schema
@Schema()
export class Buyer extends User {
  @Prop({ type: [Types.ObjectId], ref: 'Transaction', required: false })
  transactions?: Types.ObjectId[];

  @Prop({ type: [Types.ObjectId], ref: 'Order', required: false })
  orders?: Types.ObjectId[];

  @Prop({ default: 0 })
  loyaltyPoints: number;
}

export type BuyerDocument = HydratedDocument<Buyer>;
export const BuyerSchema = SchemaFactory.createForClass(Buyer);

// Farmer Schema
@Schema()
export class Farmer extends User {
  @Prop({ required: true })
  farmName: string;

  @Prop()
  farmAddress?: string;

  @Prop()
  farmSize?: string;

  @Prop()
  farmerAccountNumber?: string;

  @Prop()
  farmerAccountName?: string;

  @Prop()
  farmerBankName?: string;

  @Prop()
  farmerBankBranch?: string;

  @Prop({ type: [Types.ObjectId], ref: 'Produce', required: false })
  produces?: Types.ObjectId[];

  @Prop({ type: [Types.ObjectId], ref: 'Transaction', required: false })
  transactions?: Types.ObjectId[];

  @Prop({ type: Types.ObjectId, ref: 'Wallet' })
  wallet?: Types.ObjectId;
}

export type FarmerDocument = HydratedDocument<Farmer>;
export const FarmerSchema = SchemaFactory.createForClass(Farmer);

/* 

I HAVE NOT IMPLEMENTED FOR FIELD AGENTS, LOGISTICS AGENT and ADMINS

*/

// Field Agent Schema
@Schema()
export class FieldAgent extends User {
  @Prop({ required: true })
  agentId: string;

  @Prop({ type: [String], required: true })
  assignedRegions: string[];

  @Prop({ type: [Types.ObjectId], ref: 'Farmer', required: false })
  managedFarmers?: Types.ObjectId[];

  @Prop({ type: Types.ObjectId, ref: 'Employee', required: false })
  employeeDetails?: Types.ObjectId;
}

export type FieldAgentDocument = FieldAgent & Document;
export const FieldAgentSchema = SchemaFactory.createForClass(FieldAgent);

// Logistics Agent Schema
@Schema()
export class LogisticsAgent extends User {
  @Prop({ required: true })
  licenseNumber: string;

  @Prop({ type: [Types.ObjectId], ref: 'Vehicle', required: false })
  vehicles?: Types.ObjectId[];

  @Prop({ type: [Types.ObjectId], ref: 'Delivery', required: false })
  deliveries?: Types.ObjectId[];

  @Prop({ default: false })
  isVerified: boolean;
}

export type LogisticsAgentDocument = LogisticsAgent & Document;
export const LogisticsAgentSchema =
  SchemaFactory.createForClass(LogisticsAgent);

// Admin Schema
@Schema()
export class Admin extends User {
  @Prop({ type: String, enum: Object.values(AdminRole), required: true })
  adminRole: AdminRole;

  @Prop({ required: true })
  adminToken: string;

  @Prop({ type: [String], required: false })
  permissions?: string[];

  @Prop({ type: [String], required: false })
  managedDepartments?: string[];
}

export type AdminDocument = Admin & Document;
export const AdminSchema = SchemaFactory.createForClass(Admin);
