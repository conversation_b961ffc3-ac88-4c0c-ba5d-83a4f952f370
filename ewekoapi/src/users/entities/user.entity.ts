import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne,
  OneToMany,
  JoinColumn,
  ManyToOne,
} from 'typeorm';
import { UserType, AdminRole } from '../../shared/enums';

// Central Users Entity - All common fields in one table
@Entity('users')
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  // Authentication fields
  @Column({ unique: true })
  username: string;

  @Column()
  password: string;

  @Column({ type: 'enum', enum: UserType })
  type: UserType;

  @Column({ default: false })
  verified: boolean;

  @Column({ default: true })
  is_active: boolean;

  @Column({ type: 'timestamp', nullable: true })
  last_login: Date;

  // Profile fields (previously in separate profiles table)
  @Column()
  first_name: string;

  @Column()
  last_name: string;

  @Column({ nullable: true })
  middle_name: string;

  @Column({ nullable: true })
  prefix: string;

  @Column({ type: 'enum', enum: ['male', 'female', 'other'], nullable: true })
  gender: string;

  @Column({ type: 'date', nullable: true })
  date_of_birth: Date;

  @Column({ nullable: true })
  profile_picture: string;

  // Contact fields - email only (phones and addresses in separate tables)
  @Column({ unique: true })
  email: string;

  // Business fields (previously in separate businesses table)
  @Column({ nullable: true })
  business_name: string;

  // Common user fields
  @Column({ default: false })
  is_premium: boolean;

  @Column({ default: false })
  is_phone_verified: boolean;

  @Column({ default: false })
  is_email_verified: boolean;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  // Relationships to type-specific tables only
  @OneToOne('Farmer', 'user')
  farmer_profile: any;

  @OneToOne('Buyer', 'user')
  buyer_profile: any;

  @OneToOne('Admin', 'user')
  admin_profile: any;

  // Relationships to other entities
  @OneToMany(() => PhoneNumber, phone => phone.user)
  phone_numbers: PhoneNumber[];

  @OneToMany(() => Address, address => address.user)
  addresses: Address[];

  @OneToMany(() => Notification, notification => notification.user)
  notifications: Notification[];
}

// Removed Profile, Contact, Business entities - all fields moved to Users table

// Farmers Entity - Farm-specific data only
@Entity('farmers')
export class Farmer {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid', unique: true })
  user_id: string;

  // Farm-specific fields only
  @Column({ nullable: true })
  farm_name: string;

  @Column({ type: 'json', nullable: true })
  farm_location: any;

  @Column({ nullable: true })
  farm_size: string;

  @Column({ nullable: true })
  farm_address: string;

  // Banking details for farmers
  @Column({ nullable: true })
  account_number: string;

  @Column({ nullable: true })
  account_name: string;

  @Column({ nullable: true })
  bank_name: string;

  @Column({ nullable: true })
  bank_branch: string;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  // Relationships
  @OneToOne(() => User, user => user.farmer_profile)
  @JoinColumn({ name: 'user_id' })
  user: User;

  // Forward references - will be resolved at runtime
  @OneToMany('Produce', 'farmer')
  produce: any[];

  @OneToMany('Order', 'farmer')
  farmer_orders: any[];

  @OneToOne('Wallet', 'farmer')
  wallet: any;
}

// Buyers Entity - Buyer-specific data only
@Entity('buyers')
export class Buyer {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid', unique: true })
  user_id: string;

  // Buyer-specific fields only
  @Column({ type: 'int', default: 0 })
  loyalty_points: number;

  @Column({ type: 'json', nullable: true })
  delivery_preferences: any;

  @Column({ type: 'json', nullable: true })
  payment_methods: any;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  // Relationships
  @OneToOne(() => User, user => user.buyer_profile)
  @JoinColumn({ name: 'user_id' })
  user: User;

  // Forward references - will be resolved at runtime
  @OneToMany('Order', 'buyer')
  orders: any[];

  @OneToMany('Cart', 'buyer')
  carts: any[];
}

// Admins Entity - Admin-specific data only
@Entity('admins')
export class Admin {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid', unique: true })
  user_id: string;

  // Admin-specific fields only
  @Column({ type: 'enum', enum: AdminRole })
  role: AdminRole;

  @Column({ type: 'json', nullable: true })
  permissions: string[];

  @Column({ type: 'json', nullable: true })
  managed_departments: string[];

  @Column({ nullable: true })
  admin_token: string;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  // Relationships
  @OneToOne(() => User, user => user.admin_profile)
  @JoinColumn({ name: 'user_id' })
  user: User;
}

// Phone Numbers Entity - Multiple phone numbers per user
@Entity('phone_numbers')
export class PhoneNumber {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  user_id: string;

  @Column()
  phone_number: string;

  @Column({ type: 'enum', enum: ['personal', 'business', 'emergency', 'other'], default: 'personal' })
  type: string;

  @Column({ default: false })
  is_primary: boolean;

  @Column({ default: false })
  is_verified: boolean;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @ManyToOne(() => User, user => user.phone_numbers)
  @JoinColumn({ name: 'user_id' })
  user: User;
}

// Address Entity - Multiple addresses per user
@Entity('addresses')
export class Address {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  user_id: string;

  @Column()
  street: string;

  @Column()
  city: string;

  @Column()
  state: string;

  @Column()
  country: string;

  @Column({ nullable: true })
  postal_code: string;

  @Column({ type: 'enum', enum: ['home', 'work', 'delivery', 'billing', 'other'], default: 'home' })
  type: string;

  @Column({ default: false })
  is_primary: boolean;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @ManyToOne(() => User, user => user.addresses)
  @JoinColumn({ name: 'user_id' })
  user: User;
}

// Notification Entity
@Entity('notifications')
export class Notification {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  user_id: string;

  @Column()
  title: string;

  @Column()
  message: string;

  @Column({ default: false })
  read: boolean;

  @Column({ type: 'enum', enum: ['info', 'warning', 'error', 'success'] })
  type: string;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @ManyToOne(() => User, user => user.notifications)
  @JoinColumn({ name: 'user_id' })
  user: User;
}

// Produce Entity
@Entity('produce')
export class Produce {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column({ unique: true })
  slug: string;

  @Column({ nullable: true })
  description: string;

  @Column({ type: 'uuid' })
  farmer_id: string;

  @Column({ type: 'uuid', nullable: true })
  category_id: string;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  price: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  quantity: number;

  @Column({ nullable: true })
  unit: string;

  @Column({ type: 'json', nullable: true })
  images: string[];

  @Column({ default: true })
  is_available: boolean;

  @Column({ default: true })
  is_active: boolean;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  // Relationships
  @ManyToOne('Farmer', 'produce')
  @JoinColumn({ name: 'farmer_id' })
  farmer: any;

  @ManyToOne('Category', 'produce')
  @JoinColumn({ name: 'category_id' })
  category: any;
}

// Category Entity
@Entity('categories')
export class Category {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  name: string;

  @Column({ unique: true })
  slug: string;

  @Column({ nullable: true })
  description: string;

  @Column({ nullable: true })
  image: string;

  @Column({ default: true })
  is_active: boolean;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @OneToMany('Produce', 'category')
  produce: any[];
}

// Order Entity
@Entity('orders')
export class Order {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  buyer_id: string;

  @Column({ type: 'uuid' })
  farmer_id: string;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  total_amount: number;

  @Column({ type: 'enum', enum: ['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled'] })
  status: string;

  @Column({ type: 'enum', enum: ['pending', 'paid', 'failed', 'refunded'] })
  payment_status: string;

  @Column({ nullable: true })
  delivery_address: string;

  @Column({ nullable: true })
  notes: string;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  // Relationships
  @ManyToOne(() => User)
  @JoinColumn({ name: 'buyer_id' })
  buyer: User;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'farmer_id' })
  farmer: User;

  @OneToMany('Transaction', 'order')
  transactions: any[];
}

// Transaction Entity
@Entity('transactions')
export class Transaction {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  order_id: string;

  @Column({ type: 'uuid' })
  buyer_id: string;

  @Column({ type: 'uuid' })
  farmer_id: string;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  amount: number;

  @Column({ type: 'enum', enum: ['pending', 'completed', 'failed', 'cancelled'] })
  status: string;

  @Column({ type: 'enum', enum: ['card', 'bank_transfer', 'wallet', 'cash'] })
  payment_method: string;

  @Column({ nullable: true })
  payment_reference: string;

  @Column({ nullable: true })
  description: string;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  // Relationships
  @ManyToOne('Order', 'transactions')
  @JoinColumn({ name: 'order_id' })
  order: any;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'buyer_id' })
  buyer: User;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'farmer_id' })
  farmer: User;
}

// Cart Entity
@Entity('carts')
export class Cart {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  buyer_id: string;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  total_cost: number;

  @Column({ default: true })
  active: boolean;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  // Relationships
  @ManyToOne(() => User)
  @JoinColumn({ name: 'buyer_id' })
  buyer: User;

  @OneToMany(() => CartItem, cartItem => cartItem.cart)
  items: CartItem[];
}

// CartItem Entity
@Entity('cart_items')
export class CartItem {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  cart_id: string;

  @Column({ type: 'uuid' })
  produce_id: string;

  @Column({ type: 'int' })
  quantity: number;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  unit_price: number;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  total_price: number;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  // Relationships
  @ManyToOne(() => Cart, cart => cart.items)
  @JoinColumn({ name: 'cart_id' })
  cart: Cart;

  @ManyToOne(() => Produce)
  @JoinColumn({ name: 'produce_id' })
  produce: Produce;
}

// OrderItem Entity
@Entity('order_items')
export class OrderItem {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  order_id: string;

  @Column({ type: 'uuid' })
  produce_id: string;

  @Column({ type: 'int' })
  quantity: number;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  unit_price: number;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  total_price: number;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  // Relationships
  @ManyToOne(() => Order)
  @JoinColumn({ name: 'order_id' })
  order: Order;

  @ManyToOne(() => Produce)
  @JoinColumn({ name: 'produce_id' })
  produce: Produce;
}

// Preferences Entity
@Entity('preferences')
export class Preferences {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  user_id: string;

  @Column({ default: true })
  email_notifications: boolean;

  @Column({ default: true })
  sms_notifications: boolean;

  @Column({ default: true })
  push_notifications: boolean;

  @Column({ default: 'en' })
  language: string;

  @Column({ default: 'light' })
  theme: string;

  @Column({ type: 'json', nullable: true })
  notification_preferences: any;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  // Relationships
  @OneToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user: User;
}

// Otp Entity
@Entity('otps')
export class Otp {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  user_id: string;

  @Column()
  code: string;

  @Column({ type: 'enum', enum: ['LOGIN', '2FA', 'D2FA', 'VRFY_EMAIL', 'VRFY_PHONE', 'PWDR'] })
  use_case: string;

  @Column({ default: false })
  used: boolean;

  @Column({ type: 'timestamp' })
  expires_at: Date;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  // Relationships
  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user: User;
}
