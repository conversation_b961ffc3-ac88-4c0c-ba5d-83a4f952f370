import {
  BadRequestException,
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Produce } from 'src/produce/schema';
import { Cart, CartDocument, CartItem } from './schema';
import { AddToCartDto } from './dto/create-cart.dto';
import { UpdateCartItemDto } from './dto/update-cart.dto';
import { PaginationQueryDto } from 'src/shared/pagination/pagination-query.dto';
import { PaginationService } from 'src/shared/pagination/pagination.service';
import { UsersService } from 'src/users/users.service';

@Injectable()
export class CartService {
  constructor(
    @InjectModel(Cart.name) private cartModel: Model<Cart>,
    @InjectModel(CartItem.name) private cartItemModel: Model<CartItem>,
    @InjectModel(Produce.name) private produceModel: Model<Produce>,
    // private readonly paginationService: PaginationService,
    // private readonly userService: UsersService,
  ) {}

  /**
   * Get cart by buyer ID or create new one if it doesn't exist
   */
  async getOrCreateCart(buyerId: string): Promise<CartDocument> {
    if (!Types.ObjectId.isValid(buyerId)) {
      throw new BadRequestException('Invalid buyer ID');
    }

    const buyerObjectId = new Types.ObjectId(buyerId);

    // Find existing cart or create a new one
    let cart = await this.cartModel.findOne({ buyer: buyerObjectId });

    if (!cart) {
      cart = await this.cartModel.create({
        buyer: buyerObjectId,
        items: [],
        totalCost: 0,
      });
    }

    return cart;
  }

  /**
   * Get cart by buyer ID
   */
  async getCartByBuyerId(buyerId: string): Promise<CartDocument> {
    if (!Types.ObjectId.isValid(buyerId)) {
      throw new BadRequestException('Invalid buyer ID');
    }

    const cart = await this.cartModel
      .findOne({ buyer: new Types.ObjectId(buyerId) })
      .populate('items.produce');

    if (!cart) {
      throw new NotFoundException(`Cart not found for buyer ${buyerId}`);
    }

    return cart;
  }

  /**
   * Add item to cart
   */
  async addItem(buyerId: string, itemDto: AddToCartDto): Promise<CartDocument> {
    const { produceId, quantity, price } = itemDto;

    if (!Types.ObjectId.isValid(produceId)) {
      throw new BadRequestException('Invalid produce ID');
    }

    if (quantity <= 0) {
      throw new BadRequestException('Quantity must be greater than 0');
    }

    if (price < 0) {
      throw new BadRequestException('Price cannot be negative');
    }

    const cart = await this.getOrCreateCart(buyerId);
    const produceObjectId = new Types.ObjectId(produceId);

    // Check if item already exists in cart
    const existingItemIndex = cart.items.findIndex((item) =>
      item.produce.equals(produceObjectId),
    );

    const totalPrice = quantity * price;

    if (existingItemIndex > -1) {
      // Update existing item
      cart.items[existingItemIndex].quantity += quantity;
      cart.items[existingItemIndex].totalPrice += totalPrice;
    } else {
      // Add new item
      cart.items.push({
        produce: produceObjectId,
        quantity,
        totalPrice,
      });
    }

    // Update total cost
    cart.totalCost = cart.items.reduce((sum, item) => sum + item.totalPrice, 0);

    return cart.save();
  }

  /**
   * Update item quantity in cart
   */
  async updateItemQuantity(
    buyerId: string,
    updateDto: UpdateCartItemDto,
  ): Promise<CartDocument> {
    const { produceId, quantity } = updateDto;

    if (!Types.ObjectId.isValid(produceId)) {
      throw new BadRequestException('Invalid produce ID');
    }

    if (quantity < 0) {
      throw new BadRequestException('Quantity cannot be negative');
    }

    const cart = await this.getCartByBuyerId(buyerId);
    const produceObjectId = new Types.ObjectId(produceId);

    // Find item in cart
    const itemIndex = cart.items.findIndex((item) =>
      item.produce.equals(produceObjectId),
    );

    if (itemIndex === -1) {
      throw new NotFoundException(
        `Item with ID ${produceId} not found in cart`,
      );
    }

    if (quantity === 0) {
      // Remove item if quantity is 0
      return this.removeItem(buyerId, produceId);
    }

    // Calculate new total price based on unit price
    const unitPrice =
      cart.items[itemIndex].totalPrice / cart.items[itemIndex].quantity;
    const newTotalPrice = quantity * unitPrice;

    // Update item
    cart.items[itemIndex].quantity = quantity;
    cart.items[itemIndex].totalPrice = newTotalPrice;

    // Update total cost
    cart.totalCost = cart.items.reduce((sum, item) => sum + item.totalPrice, 0);

    return cart.save();
  }

  /**
   * Remove item from cart
   */
  async removeItem(buyerId: string, itemId: string): Promise<CartDocument> {
    if (!Types.ObjectId.isValid(itemId)) {
      throw new BadRequestException('Invalid cart item ID');
    }

    const cart = await this.getCartByBuyerId(buyerId);
    const cartItemId = new Types.ObjectId(itemId);

    // Correct: check item._id (not item.produce)

    const itemIndex = cart.items.findIndex((item) =>
      (item as any)._id.equals(cartItemId),
    );

    if (itemIndex === -1) {
      throw new NotFoundException(`Item with ID ${itemId} not found in cart`);
    }

    // Remove item
    cart.items.splice(itemIndex, 1);

    // Recalculate total cost
    cart.totalCost = cart.items.reduce((sum, item) => sum + item.totalPrice, 0);

    return cart.save();
  }

  /**
   * Clear all items from cart
   */
  async clearCart(buyerId: string): Promise<CartDocument> {
    const cart = await this.getCartByBuyerId(buyerId);

    cart.items = [];
    cart.totalCost = 0;

    return cart.save();
  }

  /**
   * Delete cart
   */
  async deleteCart(buyerId: string): Promise<{ deleted: boolean }> {
    if (!Types.ObjectId.isValid(buyerId)) {
      throw new BadRequestException('Invalid buyer ID');
    }

    const result = await this.cartModel.deleteOne({
      buyer: new Types.ObjectId(buyerId),
    });

    return { deleted: result.deletedCount > 0 };
  }
}
