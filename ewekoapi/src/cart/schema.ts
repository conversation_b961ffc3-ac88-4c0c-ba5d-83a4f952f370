// import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
// import { Types, HydratedDocument } from 'mongoose';

// export type CartDocument = HydratedDocument<Cart>;

// @Schema({ timestamps: true })
// export class CartItem {

//   @Prop({ type: Types.ObjectId, ref: 'Produce', required: true })
//   produce: Types.ObjectId;

//   @Prop({ type: Number, required: true, min: 1 })
//   quantity: number;

//   @Prop({ type: Number, required: true })
//   totalPrice: number;
// }

// export const CartItemSchema = SchemaFactory.createForClass(CartItem);

// @Schema({ timestamps: true })
// export class Cart {

//   @Prop({ type: Types.ObjectId, ref: 'User', required: true })
//   userId: Types.ObjectId;

//   @Prop({ type: [CartItemSchema], default: [] })
//   items: CartItem[];

//   @Prop({ type: Number, default: 0 })
//   totalCost: number;
// }

// export const CartSchema = SchemaFactory.createForClass(Cart);

import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Types, Schema as MongooseSchema, HydratedDocument } from 'mongoose';

@Schema()
export class CartItem {
  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Produce', required: true })
  produce: Types.ObjectId; 

  @Prop({ type: Number, required: true, min: 1 })
  quantity: number;

  @Prop({ type: Number, required: true, min: 0 })
  totalPrice: number;
}
export type CartItemDocument = HydratedDocument<CartItem>;
export const CartItemSchema = SchemaFactory.createForClass(CartItem);

@Schema({ timestamps: true })
export class Cart {
  // Enforces: One cart per buyer
  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: 'Buyer',
    required: true,
    unique: true,
    index: true,
  })
  buyer: Types.ObjectId;

  @Prop({ type: [CartItemSchema], default: [] })
  items: CartItem[];

  @Prop({ type: Number, default: 0, min: 0 })
  totalCost: number;
}
export type CartDocument = HydratedDocument<Cart>;
export const CartSchema = SchemaFactory.createForClass(Cart);

// Ensure compound index for faster lookups or updates involving specific items
CartSchema.index({ buyer: 1, 'items.produce': 1 });
